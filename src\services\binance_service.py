"""
Servicio para manejar la conexión y datos de Binance
"""
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import pandas as pd
from binance.client import Client
from binance.exceptions import BinanceAPIException, BinanceRequestException

from config.settings import BINANCE_CONFIG
from config.constants import BINANCE_INTERVALS

logger = logging.getLogger(__name__)

class BinanceService:
    """Servicio para interactuar con la API de Binance"""
    
    def __init__(self):
        """Inicializar el servicio de Binance"""
        self.client = Client()
        self.config = BINANCE_CONFIG
        self._setup_logging()
    
    def _setup_logging(self):
        """Configurar logging para el servicio"""
        logger.setLevel(logging.INFO)
    
    def get_current_price(self, symbol: str = None) -> float:
        """
        Obtener precio actual de una criptomoneda
        
        Args:
            symbol: Símbolo de la criptomoneda (ej: BTCUSDT)
            
        Returns:
            float: Precio actual o 0.0 si hay error
        """
        try:
            symbol = symbol or self.config["symbol"]
            ticker = self.client.get_symbol_ticker(symbol=symbol)
            price = float(ticker['price'])
            logger.info(f"Precio actual de {symbol}: ${price:.2f}")
            return price
        except (BinanceAPIException, BinanceRequestException) as e:
            logger.error(f"Error obteniendo precio de {symbol}: {e}")
            return 0.0
        except Exception as e:
            logger.error(f"Error inesperado obteniendo precio: {e}")
            return 0.0
    
    def get_klines_data(self, 
                        symbol: str = None, 
                        interval: str = None, 
                        limit: int = None) -> pd.DataFrame:
        """
        Obtener datos históricos de velas (klines)
        
        Args:
            symbol: Símbolo de la criptomoneda
            interval: Intervalo de tiempo
            limit: Número de registros a obtener
            
        Returns:
            pd.DataFrame: DataFrame con los datos históricos
        """
        try:
            symbol = symbol or self.config["symbol"]
            interval = interval or self.config["interval"]
            limit = limit or self.config["limit"]
            
            logger.info(f"Obteniendo {limit} registros de {symbol} en intervalos de {interval}")
            
            klines = self.client.get_klines(
                symbol=symbol,
                interval=interval,
                limit=limit
            )
            
            # Procesar datos
            data = []
            for kline in klines:
                data.append({
                    'timestamp': pd.to_datetime(kline[0], unit='ms'),
                    'open': float(kline[1]),
                    'high': float(kline[2]),
                    'low': float(kline[3]),
                    'close': float(kline[4]),
                    'volume': float(kline[5])
                    # Eliminadas quote_volume y trades - no se usan en el modelo
                })
            
            df = pd.DataFrame(data)
            logger.info(f"Datos obtenidos exitosamente: {len(df)} registros")
            return df
            
        except (BinanceAPIException, BinanceRequestException) as e:
            logger.error(f"Error de API de Binance: {e}")
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error inesperado obteniendo datos: {e}")
            return pd.DataFrame()
    
    def test_connection(self) -> bool:
        """
        Probar la conexión con Binance
        
        Returns:
            bool: True si la conexión es exitosa
        """
        try:
            # Intentar obtener información del servidor
            server_time = self.client.get_server_time()
            logger.info("Conexión con Binance establecida correctamente")
            return True
        except Exception as e:
            logger.error(f"Error de conexión con Binance: {e}")
            return False
    
    def get_historical_data_extended(self, 
                                   symbol: str = None, 
                                   interval: str = None, 
                                   months: int = 6) -> pd.DataFrame:
        """
        Obtener datos históricos extendidos usando múltiples requests
        
        Args:
            symbol: Símbolo de la criptomoneda
            interval: Intervalo de tiempo
            months: Número de meses de datos históricos
            
        Returns:
            pd.DataFrame: DataFrame con los datos históricos
        """
        try:
            symbol = symbol or self.config["symbol"]
            interval = interval or self.config["interval"]
            
            logger.info(f"Obteniendo {months} meses de datos de {symbol} en intervalos de {interval}")
            
            from datetime import datetime, timedelta
            import time
            
            # Calcular fechas
            end_time = int(datetime.now().timestamp() * 1000)  # Ahora en milisegundos
            start_time = int((datetime.now() - timedelta(days=months * 30)).timestamp() * 1000)
            
            all_data = []
            current_start = start_time
            batch_size = 1000  # Límite de Binance por request
            
            # Calcular duración de cada batch según el intervalo
            interval_minutes = {
                '1m': 1, '3m': 3, '5m': 5, '15m': 15, '30m': 30,
                '1h': 60, '2h': 120, '4h': 240, '6h': 360, '8h': 480,
                '12h': 720, '1d': 1440, '3d': 4320, '1w': 10080, '1M': 43200
            }
            
            minutes_per_batch = batch_size * interval_minutes.get(interval, 15)
            milliseconds_per_batch = minutes_per_batch * 60 * 1000
            
            batch_count = 0
            while current_start < end_time:
                batch_count += 1
                current_end = min(current_start + milliseconds_per_batch, end_time)
                
                logger.info(f"Obteniendo batch {batch_count}... ({datetime.fromtimestamp(current_start/1000).strftime('%Y-%m-%d')} a {datetime.fromtimestamp(current_end/1000).strftime('%Y-%m-%d')})")
                
                # Request a Binance
                klines = self.client.get_klines(
                    symbol=symbol,
                    interval=interval,
                    startTime=current_start,
                    endTime=current_end,
                    limit=batch_size
                )
                
                if not klines:
                    logger.warning(f"No se obtuvieron datos para batch {batch_count}")
                    break
                
                # Convertir a DataFrame
                df_batch = pd.DataFrame(klines, columns=[
                    'timestamp', 'open', 'high', 'low', 'close', 'volume',
                    'close_time', 'quote_volume', 'trades', 'taker_buy_base',
                    'taker_buy_quote', 'ignore'
                ])
                
                # Procesar datos (solo las columnas que necesitamos)
                for col in ['open', 'high', 'low', 'close', 'volume']:
                    df_batch[col] = pd.to_numeric(df_batch[col], errors='coerce')
                
                df_batch['timestamp'] = pd.to_numeric(df_batch['timestamp'], errors='coerce')
                
                # Agregar a la lista
                all_data.append(df_batch)
                logger.info(f"Batch {batch_count} completado: {len(df_batch)} registros")
                
                # Actualizar para próximo batch
                current_start = current_end + 1
                
                # Pausa para respetar rate limits
                time.sleep(0.1)
            
            if not all_data:
                logger.error("No se obtuvieron datos históricos")
                return None
            
            # Combinar todos los DataFrames
            df_final = pd.concat(all_data, ignore_index=True)
            
            # Eliminar duplicados por timestamp
            df_final = df_final.drop_duplicates(subset=['timestamp']).sort_values('timestamp')
            
            # Seleccionar solo las columnas necesarias (eliminadas quote_volume y trades)
            df_final = df_final[['timestamp', 'open', 'high', 'low', 'close', 'volume']]
            
            logger.info(f"Datos históricos obtenidos exitosamente:")
            logger.info(f"  Total registros: {len(df_final)}")
            logger.info(f"  Período: {datetime.fromtimestamp(df_final['timestamp'].min()/1000).strftime('%Y-%m-%d')} a {datetime.fromtimestamp(df_final['timestamp'].max()/1000).strftime('%Y-%m-%d')}")
            logger.info(f"  Duración: {(df_final['timestamp'].max() - df_final['timestamp'].min()) / (1000 * 60 * 60 * 24):.1f} días")
            
            return df_final
            
        except Exception as e:
            logger.error(f"Error obteniendo datos históricos extendidos: {e}")
            return None
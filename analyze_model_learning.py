#!/usr/bin/env python3
"""
Análisis detallado de cómo aprende el modelo LSTM
Investiga por qué el modelo predice siempre valores alrededor de 80K
"""

import sys
import os
import numpy as np
import pandas as pd
from pathlib import Path
import matplotlib.pyplot as plt
import pickle

# Agregar src al path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from src.services.binance_service import BinanceService
from src.ai.predictors.price_predictor import PricePredictor
from src.ai.models.lstm_model import LSTMModel

def analyze_training_data():
    """Analizar los datos que se usaron para entrenar el modelo"""
    print("🔍 ANÁLISIS DE DATOS DE ENTRENAMIENTO")
    print("=" * 50)
    
    try:
        # Obtener datos similares a los del entrenamiento
        binance_service = BinanceService()
        print("📊 Obteniendo datos históricos de 6 meses...")
        
        # Obtener datos de 6 meses (como en el entrenamiento)
        data = binance_service.get_historical_data_extended('BTCUSDT', '15m', 6)
        
        if data is not None and len(data) > 0:
            print(f"✅ Datos obtenidos: {len(data)} registros")
            print(f"📅 Período: {data['timestamp'].min()} a {data['timestamp'].max()}")
            print(f"💰 Rango de precios: ${data['close'].min():.2f} - ${data['close'].max():.2f}")
            print(f"📈 Precio promedio: ${data['close'].mean():.2f}")
            print(f"📊 Mediana: ${data['close'].median():.2f}")
            print(f"📉 Desviación estándar: ${data['close'].std():.2f}")
            
            # Analizar distribución de precios
            percentiles = [10, 25, 50, 75, 90, 95, 99]
            print("\n📊 DISTRIBUCIÓN DE PRECIOS:")
            for p in percentiles:
                value = np.percentile(data['close'], p)
                print(f"   Percentil {p:2d}%: ${value:,.2f}")
            
            # Analizar cambios de precio
            price_changes = data['close'].pct_change().dropna() * 100
            print("\n📈 ANÁLISIS DE CAMBIOS DE PRECIO:")
            print(f"   Cambio promedio: {price_changes.mean():.4f}%")
            print(f"   Cambio mediano: {price_changes.median():.4f}%")
            print(f"   Desv. estándar: {price_changes.std():.4f}%")
            print(f"   Cambio máximo: {price_changes.max():.2f}%")
            print(f"   Cambio mínimo: {price_changes.min():.2f}%")
            
            return data
        else:
            print("❌ Error obteniendo datos")
            return None
            
    except Exception as e:
        print(f"❌ Error en análisis de datos: {e}")
        return None

def analyze_model_scalers():
    """Analizar los scalers del modelo entrenado"""
    print("\n🔧 ANÁLISIS DE SCALERS DEL MODELO")
    print("=" * 50)
    
    try:
        scalers_path = "models/btc_lstm_model_scalers.pkl"
        if os.path.exists(scalers_path):
            with open(scalers_path, 'rb') as f:
                scalers = pickle.load(f)
            
            print(f"📁 Scalers cargados desde: {scalers_path}")
            print(f"🔑 Claves disponibles: {list(scalers.keys())}")
            
            # Analizar price_scaler (target_scaler)
            if 'price_scaler' in scalers:
                price_scaler = scalers['price_scaler']
                print("\n💰 PRICE SCALER (MinMaxScaler):")
                print(f"   Tipo: {type(price_scaler).__name__}")
                print(f"   Rango: {price_scaler.feature_range}")
                print(f"   Data min: ${price_scaler.data_min_[0]:,.2f}")
                print(f"   Data max: ${price_scaler.data_max_[0]:,.2f}")
                print(f"   Scale: {price_scaler.scale_[0]:.8f}")
                print(f"   Min: {price_scaler.min_[0]:.8f}")
                
                # Calcular qué valores normalizados corresponden a precios específicos
                test_prices = [70000, 80000, 90000, 100000, 110000, 120000]
                print("\n🧮 MAPEO PRECIO → VALOR NORMALIZADO:")
                for price in test_prices:
                    if price >= price_scaler.data_min_[0] and price <= price_scaler.data_max_[0]:
                        normalized = price_scaler.transform([[price]])[0][0]
                        print(f"   ${price:,} → {normalized:.6f}")
                    else:
                        print(f"   ${price:,} → FUERA DE RANGO")
                
                # Analizar qué precio corresponde a diferentes valores normalizados
                test_normalized = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
                print("\n🔄 MAPEO VALOR NORMALIZADO → PRECIO:")
                for norm_val in test_normalized:
                    price = price_scaler.inverse_transform([[norm_val]])[0][0]
                    print(f"   {norm_val:.1f} → ${price:,.2f}")
            
            # Analizar feature_scaler
            if 'feature_scaler' in scalers:
                feature_scaler = scalers['feature_scaler']
                print(f"\n📊 FEATURE SCALER:")
                print(f"   Tipo: {type(feature_scaler).__name__}")
                print(f"   Número de características: {len(feature_scaler.data_min_)}")
                
            return scalers
        else:
            print(f"❌ Archivo de scalers no encontrado: {scalers_path}")
            return None
            
    except Exception as e:
        print(f"❌ Error analizando scalers: {e}")
        return None

def analyze_model_predictions():
    """Analizar las predicciones del modelo en diferentes escenarios"""
    print("\n🔮 ANÁLISIS DE PREDICCIONES DEL MODELO")
    print("=" * 50)
    
    try:
        # Cargar modelo
        predictor = PricePredictor()
        predictor.load_model("models/btc_lstm_model.h5")
        
        if not predictor.is_ready:
            print("❌ Modelo no está listo")
            return
        
        # Obtener datos actuales
        binance_service = BinanceService()
        current_data = binance_service.get_historical_data('BTCUSDT', '15m', 100)
        
        if current_data is None or len(current_data) == 0:
            print("❌ Error obteniendo datos actuales")
            return
        
        print(f"📊 Datos actuales: {len(current_data)} registros")
        print(f"💰 Precio actual: ${current_data['close'].iloc[-1]:.2f}")
        
        # Realizar múltiples predicciones con diferentes ventanas de datos
        print("\n🎯 PREDICCIONES CON DIFERENTES VENTANAS:")
        
        window_sizes = [50, 75, 100]
        for window_size in window_sizes:
            if len(current_data) >= window_size:
                test_data = current_data.tail(window_size).copy()
                
                try:
                    result = predictor.predict_next_price(test_data)
                    
                    if isinstance(result, dict):
                        predicted_price = result.get('predicted_price', 0)
                        confidence = result.get('confidence', 0)
                        change_pct = result.get('change_percentage', 0)
                    else:
                        predicted_price = result[0] if len(result) > 0 else 0
                        confidence = result[1] if len(result) > 1 else 0
                        current_price = test_data['close'].iloc[-1]
                        change_pct = ((predicted_price - current_price) / current_price) * 100
                    
                    print(f"   Ventana {window_size:3d}: ${predicted_price:8.2f} ({change_pct:+6.2f}%) - Confianza: {confidence:.1%}")
                    
                except Exception as e:
                    print(f"   Ventana {window_size:3d}: ERROR - {e}")
        
        # Analizar predicción con datos sintéticos en diferentes rangos de precio
        print("\n🧪 PREDICCIONES CON DATOS SINTÉTICOS:")
        
        base_data = current_data.tail(50).copy()
        test_price_levels = [75000, 85000, 95000, 105000, 115000, 125000]
        
        for test_price in test_price_levels:
            # Crear datos sintéticos con precio fijo
            synthetic_data = base_data.copy()
            synthetic_data['close'] = test_price
            synthetic_data['open'] = test_price * 0.999
            synthetic_data['high'] = test_price * 1.001
            synthetic_data['low'] = test_price * 0.998
            
            try:
                result = predictor.predict_next_price(synthetic_data)
                
                if isinstance(result, dict):
                    predicted_price = result.get('predicted_price', 0)
                    change_pct = result.get('change_percentage', 0)
                else:
                    predicted_price = result[0] if len(result) > 0 else 0
                    change_pct = ((predicted_price - test_price) / test_price) * 100
                
                print(f"   Precio ${test_price:,} → Predicción: ${predicted_price:8.2f} ({change_pct:+6.2f}%)")
                
            except Exception as e:
                print(f"   Precio ${test_price:,} → ERROR: {e}")
        
    except Exception as e:
        print(f"❌ Error en análisis de predicciones: {e}")

def analyze_model_architecture():
    """Analizar la arquitectura del modelo"""
    print("\n🏗️ ANÁLISIS DE ARQUITECTURA DEL MODELO")
    print("=" * 50)
    
    try:
        # Cargar modelo directamente
        from tensorflow.keras.models import load_model
        
        model_path = "models/btc_lstm_model.h5"
        if os.path.exists(model_path):
            model = load_model(model_path)
            
            print(f"📁 Modelo cargado desde: {model_path}")
            print(f"🔧 Número de capas: {len(model.layers)}")
            
            print("\n📋 RESUMEN DE CAPAS:")
            for i, layer in enumerate(model.layers):
                print(f"   {i+1:2d}. {layer.__class__.__name__:15s} - {layer.output_shape}")
                if hasattr(layer, 'units'):
                    print(f"       Unidades: {layer.units}")
                if hasattr(layer, 'activation'):
                    print(f"       Activación: {layer.activation.__name__}")
            
            print(f"\n📊 PARÁMETROS TOTALES: {model.count_params():,}")
            
            # Analizar configuración del optimizador
            print("\n⚙️ CONFIGURACIÓN DE ENTRENAMIENTO:")
            print(f"   Optimizador: {model.optimizer.__class__.__name__}")
            print(f"   Learning Rate: {model.optimizer.learning_rate.numpy():.6f}")
            print(f"   Función de pérdida: {model.loss}")
            
        else:
            print(f"❌ Modelo no encontrado: {model_path}")
            
    except Exception as e:
        print(f"❌ Error analizando arquitectura: {e}")

def main():
    """Función principal de análisis"""
    print("🧠 ANÁLISIS COMPLETO DEL APRENDIZAJE DEL MODELO LSTM")
    print("=" * 60)
    print("Investigando por qué el modelo predice siempre ~80K")
    print("=" * 60)
    
    # 1. Analizar datos de entrenamiento
    training_data = analyze_training_data()
    
    # 2. Analizar scalers
    scalers = analyze_model_scalers()
    
    # 3. Analizar arquitectura
    analyze_model_architecture()
    
    # 4. Analizar predicciones
    analyze_model_predictions()
    
    print("\n🎯 CONCLUSIONES DEL ANÁLISIS:")
    print("=" * 50)
    
    if scalers and 'price_scaler' in scalers:
        price_scaler = scalers['price_scaler']
        min_price = price_scaler.data_min_[0]
        max_price = price_scaler.data_max_[0]
        
        print(f"1. 📊 RANGO DE ENTRENAMIENTO: ${min_price:,.2f} - ${max_price:,.2f}")
        
        # Calcular qué valor normalizado corresponde a ~80K
        if 80000 >= min_price and 80000 <= max_price:
            normalized_80k = price_scaler.transform([[80000]])[0][0]
            print(f"2. 🎯 $80,000 corresponde al valor normalizado: {normalized_80k:.6f}")
        
        # Verificar si el modelo está prediciendo un valor normalizado específico
        print(f"3. 🧠 El modelo probablemente aprendió a predecir valores en la parte baja del rango")
        print(f"4. 📈 Solución: Reentrenar con datos más recientes que incluyan precios actuales")
    
    print("\n✅ Análisis completado")

if __name__ == "__main__":
    main()
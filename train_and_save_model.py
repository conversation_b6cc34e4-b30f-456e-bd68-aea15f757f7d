#!/usr/bin/env python3
"""
Script para entrenar y guardar el modelo de IA - OPTIMIZADO CON VALIDACIONES CONSERVADORAS
Usando 6 meses de datos históricos con limpieza avanzada y early stopping dinámico
"""
import sys
import os
import logging
from pathlib import Path
import pandas as pd
import numpy as np

def simple_data_validation(df):
    """
    Validación simple de datos - Solo verifica integridad básica
    NO elimina movimientos de precio legítimos (outliers son eventos reales del mercado)
    """
    print(f"   📊 Validando datos básicos: {df.shape}")
    
    # Verificar datos faltantes
    missing_data = df.isnull().sum().sum()
    if missing_data > 0:
        print(f"   ⚠️ Encontrados {missing_data} valores faltantes")
        df = df.dropna().copy()
    
    # Verificar que high >= low >= 0 (validación lógica básica)
    invalid_prices = (df['high'] < df['low']) | (df['low'] < 0)
    invalid_count = invalid_prices.sum()
    
    if invalid_count > 0:
        print(f"   🔧 Corrigiendo {invalid_count} precios inválidos")
        df = df[~invalid_prices].copy()
    
    print(f"   ✅ Datos validados: {df.shape}")
    print(f"   💰 Rango de precios: ${df['close'].min():.2f} - ${df['close'].max():.2f}")
    
    return df

def train_and_save_model():
    """Entrenar y guardar el modelo de IA con validaciones avanzadas"""
    print("🧠 Entrenando y Guardando Modelo de IA con Validaciones Avanzadas...")
    print("=" * 60)
    
    try:
        # Agregar src al path
        src_path = Path(__file__).parent / "src"
        sys.path.insert(0, str(src_path))
        
        print("1. Obteniendo datos reales de Binance...")
        from src.services.binance_service import BinanceService
        
        # Crear servicio de Binance
        binance_service = BinanceService()
        
        # Obtener datos históricos de 6 meses con intervalos de 15 minutos
        # Usando método extendido con múltiples requests startTime/endTime
        print("   📊 Obteniendo 6 meses de datos históricos (intervalos de 15m)...")
        print("   ⏳ Esto tomará varios minutos debido a múltiples requests...")
        
        raw_data = binance_service.get_historical_data_extended('BTCUSDT', '15m', 6)
        
        if raw_data is not None and len(raw_data) > 0:
            print(f"   🎉 Datos históricos obtenidos exitosamente!")
            print(f"   📊 Total registros: {len(raw_data):,}")
            print(f"   ⏰ Cobertura real: {(raw_data['timestamp'].max() - raw_data['timestamp'].min()) / (1000 * 60 * 60 * 24):.1f} días")
            print(f"   📅 Intervalo: 15 minutos (granularidad máxima)")
        else:
            print("   ❌ Error obteniendo datos históricos extendidos de Binance")
            return False
        
        if raw_data is None or len(raw_data) == 0:
            print("   ❌ Error obteniendo datos de Binance")
            return False
        
        # Usar datos reales con limpieza avanzada
        print(f"   ✅ Datos históricos obtenidos: {len(raw_data)} registros")
        print(f"   📊 Rango de precios bruto: ${raw_data['close'].min():.2f} - ${raw_data['close'].max():.2f}")
        print(f"   📅 Período: {raw_data['timestamp'].min()} a {raw_data['timestamp'].max()}")
        
        # DEBUG: Verificar columnas disponibles
        print(f"   🔍 Columnas disponibles: {list(raw_data.columns)}")
        print(f"   📈 Muestra de datos OHLCV:")
        print(f"      - Open: {raw_data['open'].iloc[-1]:.2f}")
        print(f"      - High: {raw_data['high'].iloc[-1]:.2f}")
        print(f"      - Low: {raw_data['low'].iloc[-1]:.2f}")
        print(f"      - Close: {raw_data['close'].iloc[-1]:.2f}")
        print(f"      - Volume: {raw_data['volume'].iloc[-1]:.0f}")
        
        # Verificar que high != low (varianza)
        high_low_diff = (raw_data['high'] - raw_data['low']).abs()
        print(f"   📊 Diferencia High-Low promedio: ${high_low_diff.mean():.4f}")
        print(f"   📊 Varianza de High: {raw_data['high'].var():.2f}")
        print(f"   📊 Varianza de Low: {raw_data['low'].var():.2f}")
        
        print("\n2. Validación básica de datos...")
        validated_data = simple_data_validation(raw_data.copy())
        print(f"   💰 Precio actual: ${validated_data['close'].iloc[-1]:.2f}")
        
        print("\n3. Importando sistema de IA...")
        from src.ai.predictors.price_predictor import PricePredictor
        
        # Crear predictor - AQUÍ se calculan los indicadores técnicos
        predictor = PricePredictor()
        print(f"   ✅ Predictor creado")
        
        print("\n4. Entrenando modelo (flujo simplificado)...")
        print("   📈 Configuración:")
        print("      - Épocas: 50 (sin early stopping complejo)")
        print("      - Validación: 20% de los datos")
        print("      - Flujo: Datos → Indicadores → Normalización → Entrenamiento")
        
        # Entrenar con flujo simplificado
        history = predictor.train_model(
            validated_data,  # Datos con OHLCV intactos
            target_column='close',
            epochs=50,  # Menos épocas, más simple
            save_model=True
        )
        
        print(f"   ✅ Modelo entrenado y guardado exitosamente")
        print(f"   📈 Historial: {len(history)} métricas")
        
        print("\n4. Verificando archivos guardados...")
        import os
        model_files = os.listdir("models")
        print(f"   📁 Archivos en carpeta models:")
        for file in model_files:
            file_size = os.path.getsize(f"models/{file}")
            print(f"      - {file} ({file_size:,} bytes)")
        
        print("\n5. Validando modelo entrenado...")        
        # Usar solo los últimos datos para validación
        validation_data = validated_data.tail(200).copy()
        
        try:
            prediction_result = predictor.predict_next_price(validation_data)
            
            # Manejar diferentes formatos de respuesta
            if isinstance(prediction_result, dict):
                current_price = validation_data['close'].iloc[-1]
                predicted_price = prediction_result.get('predicted_price', 0)
                change_pct = prediction_result.get('change_percentage', 0)
                confidence = prediction_result.get('confidence', 0)
            else:
                # Formato legacy (tupla)
                current_price = validation_data['close'].iloc[-1]
                predicted_price = prediction_result[0] if len(prediction_result) > 0 else 0
                confidence = prediction_result[1] if len(prediction_result) > 1 else 0
                change_pct = ((predicted_price - current_price) / current_price) * 100 if current_price > 0 else 0
            
            print(f"   💰 Precio actual: ${current_price:.2f}")
            print(f"   🔮 Precio predicho: ${predicted_price:.2f}")
            print(f"   📈 Cambio esperado: {change_pct:+.2f}%")
            print(f"   🎯 Confianza: {confidence:.1%}")
            
            # Validar predicción optimizada para trading de ±2%
            if abs(change_pct) > 15:  # >15% es extremadamente sospechoso
                print(f"   🚨 PREDICCIÓN EXTREMA: {change_pct:+.2f}% - Modelo necesita reentrenamiento")
                print("   🔧 Recomendación: Reducir épocas, ajustar learning rate o revisar datos")
            elif abs(change_pct) > 10:  # 10-15% es muy alto pero posible en crypto
                print(f"   ⚠️ PREDICCIÓN MUY ALTA: {change_pct:+.2f}% - Usar con precaución")
                print(f"   💡 Considerar reducir tamaño de posición para este nivel de volatilidad")
            elif abs(change_pct) >= 2:  # 2-10% es el rango objetivo para trading
                print(f"   ✅ PREDICCIÓN ÓPTIMA PARA TRADING: {change_pct:+.2f}%")
                print(f"   🎯 Rango ideal para señales LONG/SHORT - ¡Excelente para trading!")
            elif abs(change_pct) >= 1.2:  # 1.2-2% es moderado pero útil
                print(f"   📊 PREDICCIÓN MODERADA: {change_pct:+.2f}%")
                print(f"   💼 Útil para trading conservador o posiciones graduales")
            else:  # <1.2% es muy bajo para trading
                print(f"   ⏸️ PREDICCIÓN BAJA: {change_pct:+.2f}%")
                print(f"   💤 Cambio insuficiente para oportunidades de trading claras")
                
        except Exception as e:
            print(f"   ❌ Error en validación: {e}")
            print(f"   🔧 Recomendación: Revisar los datos de entrada y parámetros del modelo")
        
        print("\n6. Probando señales de trading optimizadas para ±2%...")
        try:
            # Generar señales con umbral de confianza optimizado para trading
            signals = predictor.generate_trading_signals(validation_data, confidence_threshold=0.4)
            if isinstance(signals, dict):
                signal = signals.get('signal', 'DESCONOCIDO')
                confidence = signals.get('confidence', 0)
                price_change_pct = signals.get('price_change_percent', 0)
                recommendation = signals.get('recommendation', 'No disponible')
                
                print(f"   📡 Señal: {signal}")
                print(f"   🎯 Confianza: {confidence:.1%}")
                print(f"   📈 Cambio esperado: {price_change_pct:+.2f}%")
                print(f"   💡 Recomendación: {recommendation}")
                
                # Evaluar calidad de la señal para trading
                if signal in ['LONG_FUERTE', 'SHORT_FUERTE'] and confidence >= 0.7:
                    print(f"   ✅ SEÑAL DE ALTA CALIDAD: Ideal para trading agresivo")
                elif signal in ['LONG', 'SHORT'] and confidence >= 0.5:
                    print(f"   📊 SEÑAL MODERADA: Buena para trading conservador")
                elif signal in ['LONG_MODERADO', 'SHORT_MODERADO']:
                    print(f"   ⚡ SEÑAL GRADUAL: Considerar entrada/salida por partes")
                elif signal == 'ESPERA':
                    print(f"   ⏳ ESPERAR: Condiciones no óptimas para trading")
                else:
                    print(f"   ⏸️ MANTENER: Sin oportunidad clara de ±2%")
                    
            else:
                print(f"   ❌ Formato de señal inesperado: {type(signals)}")
        except Exception as e:
            print(f"   ❌ Error generando señales: {e}")
            print(f"   🔧 Esto puede deberse a discrepancias en las características del modelo")
        
        print("\n7. Evaluando precisión de señales de trading...")
        try:
            # Evaluar múltiples predicciones para calcular métricas de trading
            test_data = validated_data.tail(100).copy()  # Últimos 100 registros
            correct_signals = 0
            total_signals = 0
            long_signals = 0
            short_signals = 0
            
            for i in range(30, len(test_data) - 1):  # Evaluar predicciones (necesitamos al menos 30 filas)
                try:
                    current_data = test_data.iloc[:i+1]
                    actual_next_price = test_data.iloc[i+1]['close']
                    current_price = test_data.iloc[i]['close']
                    
                    # Asegurar que tenemos suficientes datos para indicadores técnicos
                    if len(current_data) < 50:  # Necesitamos datos suficientes para SMA_50
                        continue
                    
                    # Generar señal con datos que incluyen indicadores técnicos
                    signals = predictor.generate_trading_signals(current_data, confidence_threshold=0.4)
                    if isinstance(signals, dict):
                        signal = signals.get('signal', 'MANTENER')
                        predicted_change = signals.get('price_change_percent', 0)
                        
                        # Calcular cambio real
                        actual_change = ((actual_next_price - current_price) / current_price) * 100
                        
                        # Evaluar precisión de la señal
                        if signal in ['LONG_FUERTE', 'LONG', 'LONG_MODERADO']:
                            long_signals += 1
                            if actual_change > 0:  # Precio subió como se predijo
                                correct_signals += 1
                        elif signal in ['SHORT_FUERTE', 'SHORT', 'SHORT_MODERADO']:
                            short_signals += 1
                            if actual_change < 0:  # Precio bajó como se predijo
                                correct_signals += 1
                        
                        total_signals += 1
                        
                except Exception:
                    continue
            
            # Calcular métricas
            if total_signals > 0:
                accuracy = (correct_signals / total_signals) * 100
                print(f"   📊 Precisión de señales: {accuracy:.1f}% ({correct_signals}/{total_signals})")
                print(f"   📈 Señales LONG generadas: {long_signals}")
                print(f"   📉 Señales SHORT generadas: {short_signals}")
                
                if accuracy >= 60:
                    print(f"   ✅ EXCELENTE: Precisión >60% - Modelo listo para trading")
                elif accuracy >= 50:
                    print(f"   📊 BUENO: Precisión >50% - Modelo útil con gestión de riesgo")
                else:
                    print(f"   ⚠️ MEJORABLE: Precisión <50% - Considerar reentrenamiento")
            else:
                print(f"   ⚠️ No se pudieron evaluar señales suficientes")
                
        except Exception as e:
            print(f"   ❌ Error evaluando métricas de trading: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 ¡MODELO ENTRENADO Y OPTIMIZADO PARA TRADING!")
        print("=" * 50)
        
        print("\n📋 RESUMEN:")
        print(f"✅ Modelo guardado en: models/btc_lstm_model.h5")
        print(f"✅ Scalers guardados en: models/btc_lstm_model_scalers.pkl")
        print(f"✅ Datos procesados: {len(validated_data)} registros")
        print(f"✅ Optimizado para señales LONG/SHORT de ±2%")
        
        print("\n🚀 RECOMENDACIONES PARA TRADING:")
        print("   🎯 Usa señales LONG_FUERTE/SHORT_FUERTE para trading agresivo")
        print("   📊 Usa señales LONG/SHORT para trading moderado")
        print("   ⚡ Usa señales MODERADAS para entradas/salidas graduales")
        print("   💡 Siempre combina con gestión de riesgo y stop-loss")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Función principal"""
    print("🧠 ENTRENAMIENTO Y GUARDADO DE MODELO DE IA")
    print("=" * 50)
    
    success = train_and_save_model()
    
    if success:
        print("\n🎯 ¡Modelo listo para usar en producción!")
    else:
        print("\n❌ Error en el entrenamiento. Revisa los errores arriba.")
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Script de Backtesting para el Modelo LSTM de Predicción de Precios

Este script evalúa la eficiencia del modelo entrenado usando datos históricos
y simula operaciones de trading para calcular métricas de rendimiento.
"""

import sys
import os
sys.path.append('src')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any

from src.ai.predictors.price_predictor import PricePredictor
from src.services.binance_service import BinanceService
from src.services.backtesting_service import BacktestingService
from src.utils.visualization import BacktestingVisualizer

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/backtesting.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ModelBacktester:
    """Clase principal para ejecutar backtesting del modelo LSTM."""
    
    def __init__(self, model_path: str = None, initial_balance: float = 10000.0):
        """Inicializar el backtester."""
        self.model_path = model_path
        self.initial_balance = initial_balance
        
        # Configuración de trading OPTIMIZADA para el modelo LSTM (más agresiva)
        self.min_confidence = 0.2  # Confianza mínima reducida para capturar más señales
        self.min_price_change = 0.003  # Cambio mínimo del 0.3% (más sensible)
        self.max_position_size = 0.8  # Máximo 80% del balance en una posición
        self.stop_loss = 0.05  # Stop loss del 5%
        self.take_profit = 0.10  # Take profit del 10%
        
        # Configuración de backtesting
        self.lookback_window = 60  # Ventana de datos para predicción
        self.prediction_interval = 1  # Intervalo entre predicciones (horas)
        
        # Inicializar servicios
        self.predictor = PricePredictor()
        self.binance_service = BinanceService()
        self.backtesting_service = BacktestingService(initial_balance)
        
        # Configurar parámetros optimizados en el servicio de backtesting
        self.backtesting_service.min_confidence = self.min_confidence
        self.backtesting_service.min_price_change = self.min_price_change
        
        # Inicializar balance de BTC para simulación manual
        self.backtesting_service.btc_balance = 0.0
        self.backtesting_service.manual_trades = []  # Lista para almacenar trades manuales
        
    def load_model(self) -> bool:
        """Cargar el modelo entrenado."""
        try:
            if not os.path.exists(self.model_path):
                logger.error(f"Modelo no encontrado en: {self.model_path}")
                return False
            
            self.predictor.load_model(self.model_path)
            logger.info(f"✅ Modelo cargado exitosamente desde: {self.model_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error cargando modelo: {e}")
            return False
    
    def get_historical_data(self, days: int = 30) -> pd.DataFrame:
        """Obtener datos históricos para backtesting con indicadores técnicos."""
        try:
            # Calcular límite de velas (24 horas * días)
            limit = min(days * 24, 1000)  # Máximo 1000 velas por limitación de API
            
            logger.info(f"📊 Obteniendo {limit} horas de datos históricos...")
            df = self.binance_service.get_klines_data(limit=limit, interval='1h')
            
            if df.empty:
                logger.error("❌ No se pudieron obtener datos históricos")
                return pd.DataFrame()
            
            logger.info(f"✅ Datos base obtenidos: {len(df)} registros")
            logger.info(f"📅 Período: {df.iloc[0]['timestamp']} a {df.iloc[-1]['timestamp']}")
            
            # CRÍTICO: Preparar datos con indicadores técnicos incluyendo ATR
            logger.info("🔧 Preparando datos con indicadores técnicos (incluyendo ATR)...")
            prepared_df = self.predictor.prepare_data_for_prediction(df)
            
            if prepared_df.empty:
                logger.error("❌ Error preparando datos con indicadores técnicos")
                return pd.DataFrame()
            
            # Verificar que ATR esté presente
            if 'atr' not in prepared_df.columns:
                logger.error("❌ ATR no encontrado en datos preparados")
                return pd.DataFrame()
            
            # Verificar si timestamp está en las columnas o ya es índice
            if 'timestamp' in prepared_df.columns:
                prepared_df = prepared_df.set_index('timestamp')
            elif prepared_df.index.name != 'timestamp':
                # Si no hay timestamp, usar el índice numérico
                logger.warning("⚠️ No se encontró columna timestamp, usando índice numérico")
            
            logger.info(f"✅ Datos preparados: {len(prepared_df)} registros con {len(prepared_df.columns)} columnas")
            logger.info(f"📊 Columnas incluidas: {list(prepared_df.columns)[:10]}...")  # Mostrar primeras 10
            logger.info(f"🎯 ATR presente: {'Si' if 'atr' in prepared_df.columns else 'No'}")
            
            return prepared_df
            
        except Exception as e:
            logger.error(f"❌ Error obteniendo datos históricos: {e}")
            return pd.DataFrame()
    
    def run_backtesting(self, historical_data: pd.DataFrame) -> Dict:
        """Ejecutar backtesting completo."""
        try:
            logger.info("🚀 INICIANDO BACKTESTING DEL MODELO LSTM")
            logger.info("=" * 60)
            
            if len(historical_data) < self.lookback_window:
                raise ValueError(f"Datos insuficientes. Se requieren al menos {self.lookback_window} registros")
            
            # Resetear backtesting
            self.backtesting_service.reset()
            
            total_predictions = 0
            successful_predictions = 0
            failed_predictions = 0
            all_predictions = []  # Almacenar todas las predicciones para visualización
            
            # Simular trading paso a paso
            for i in range(self.lookback_window, len(historical_data)):
                try:
                    # Datos hasta el punto actual (simulando tiempo real)
                    current_data = historical_data.iloc[:i+1].copy()
                    current_price = current_data.iloc[-1]['close']
                    timestamp = current_data.index[-1]
                    
                    # Generar señal de trading con umbral optimizado
                    signals = self.predictor.generate_trading_signals(
                        current_data, 
                        confidence_threshold=self.min_confidence
                    )
                    
                    if isinstance(signals, dict) and 'predicted_price' in signals:
                        predicted_price = signals['predicted_price']
                        confidence = signals.get('confidence', 0.0)
                        
                        # Almacenar predicción para visualización
                        # Asegurar que timestamp sea datetime
                        if isinstance(timestamp, (int, float)):
                            # Si es timestamp Unix, convertir
                            timestamp_dt = pd.to_datetime(timestamp, unit='ms')
                        else:
                            # Si ya es datetime o string, convertir
                            timestamp_dt = pd.to_datetime(timestamp)
                        
                        prediction_data = {
                            'timestamp': timestamp_dt,
                            'actual_price': current_price,
                            'predicted_price': predicted_price,
                            'confidence': confidence,
                            'signal': signals.get('signal', 'ESPERA')
                        }
                        all_predictions.append(prediction_data)
                        
                        # Debug: Mostrar información de confianza
                        logger.info(f"Confianza: {confidence:.3f}, Umbral: {self.min_confidence:.3f}, Senal: {signals.get('signal', 'ESPERA')}")
                        
                        # Convertir señal del modelo a formato de backtesting
                        trading_signal = self._convert_model_signal_to_trading(signals.get('signal', 'ESPERA'))
                        
                        logger.info(f"Senal generada: {signals.get('signal', 'ESPERA')} -> {trading_signal}")
                        
                        # Procesar señal con el servicio de backtesting usando la señal convertida
                        if trading_signal != 'HOLD':
                            # Simular el procesamiento manual de la señal
                            result = self._execute_manual_trade(
                                trading_signal, current_price, predicted_price, confidence, timestamp
                            )
                        else:
                            result = {
                                 'action': 'hold',
                                 'price': current_price,
                                 'timestamp': timestamp,
                                 'portfolio_value': self.backtesting_service.current_balance
                             }
                        
                        total_predictions += 1
                        successful_predictions += 1
                        
                        # Log cada 50 predicciones
                        if total_predictions % 50 == 0:
                            logger.info(f"📈 Procesadas {total_predictions} predicciones...")
                    
                    else:
                        failed_predictions += 1
                        
                except Exception as e:
                    failed_predictions += 1
                    logger.debug(f"Error en predicción {i}: {e}")
                    continue
            
            # Obtener métricas finales
            metrics = self.backtesting_service.get_performance_metrics()
            
            # Calcular métricas de trades manuales
            total_trades = len(self.backtesting_service.manual_trades)
            buy_trades = [t for t in self.backtesting_service.manual_trades if t['type'] == 'BUY']
            sell_trades = [t for t in self.backtesting_service.manual_trades if t['type'] == 'SELL']
            
            # Calcular P&L de pares de trades (BUY-SELL)
            winning_trades = 0
            losing_trades = 0
            total_pnl = 0
            
            for i in range(min(len(buy_trades), len(sell_trades))):
                buy_price = buy_trades[i]['price']
                sell_price = sell_trades[i]['price']
                amount = buy_trades[i]['amount']
                pnl = (sell_price - buy_price) * amount
                total_pnl += pnl
                
                if pnl > 0:
                    winning_trades += 1
                else:
                    losing_trades += 1
            
            completed_trades = winning_trades + losing_trades
            win_rate = (winning_trades / completed_trades * 100) if completed_trades > 0 else 0
            avg_profit_per_trade = total_pnl / completed_trades if completed_trades > 0 else 0
            
            # Valor final del portfolio
            current_price = historical_data.iloc[-1]['close']
            current_btc_value = self.backtesting_service.btc_balance * current_price
            final_portfolio_value = self.backtesting_service.current_balance + current_btc_value
            total_return = ((final_portfolio_value - self.initial_balance) / self.initial_balance) * 100
            
            # Agregar estadísticas de predicción y trading
            metrics.update({
                'total_predictions': total_predictions,
                'successful_predictions': successful_predictions,
                'failed_predictions': failed_predictions,
                'prediction_success_rate': (successful_predictions / total_predictions * 100) if total_predictions > 0 else 0,
                'total_trades': total_trades,
                'completed_trades': completed_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'avg_profit_per_trade': avg_profit_per_trade,
                'total_profit_loss': total_pnl,
                'current_portfolio_value': final_portfolio_value,
                'total_return_pct': total_return,
                'final_price': current_price
            })
            
            # Generar visualización con las predicciones capturadas
            self._generate_visualization(historical_data, all_predictions, metrics)
            
            logger.info("BACKTESTING COMPLETADO")
            return metrics
            
        except Exception as e:
            logger.error(f"❌ Error en backtesting: {e}")
            return {}
    
    def _convert_model_signal_to_trading(self, model_signal: str) -> str:
        """Convertir señales del modelo LSTM a señales de trading."""
        signal_mapping = {
            'LONG_FUERTE': 'BUY',
            'LONG_MODERADO': 'BUY',
            'SHORT_FUERTE': 'SELL',
            'SHORT_MODERADO': 'SELL',
            'ESPERA': 'HOLD',
            'NEUTRAL': 'HOLD'
        }
        
        # Para testing: Si no tengo BTC y recibo SELL, convertir a BUY para poder probar el ciclo completo
        if model_signal in ['SHORT_FUERTE', 'SHORT_MODERADO'] and self.backtesting_service.btc_balance == 0:
            logger.info(f"CONVERSION TEMPORAL: {model_signal} -> BUY (para testing, sin BTC para vender)")
            return 'BUY'
        
        return signal_mapping.get(model_signal, 'HOLD')
    
    def _execute_manual_trade(self, signal: str, current_price: float, predicted_price: float, 
                              confidence: float, timestamp) -> Dict:
        """Ejecutar operación manual simulando el comportamiento del BacktestingService."""
        try:
            # Asegurar que timestamp sea datetime
            if isinstance(timestamp, (int, float)):
                timestamp_dt = pd.to_datetime(timestamp, unit='ms')
            else:
                timestamp_dt = pd.to_datetime(timestamp)
            
            if signal == 'BUY' and self.backtesting_service.current_balance > 100:  # Mínimo $100 para operar
                # Simular compra
                amount_to_invest = self.backtesting_service.current_balance * self.max_position_size
                btc_amount = amount_to_invest / current_price
                
                # Actualizar balances
                self.backtesting_service.current_balance -= amount_to_invest
                self.backtesting_service.btc_balance += btc_amount
                
                # Registrar trade
                trade = {
                    'type': 'BUY',
                    'price': current_price,
                    'amount': btc_amount,
                    'timestamp': timestamp_dt,
                    'confidence': confidence,
                    'predicted_price': predicted_price
                }
                self.backtesting_service.manual_trades.append(trade)
                
                logger.info(f"COMPRA EJECUTADA: {btc_amount:.6f} BTC a ${current_price:,.2f} - Balance USD: ${self.backtesting_service.current_balance:,.2f}")
                
                return {
                    'action': 'buy',
                    'price': current_price,
                    'amount': btc_amount,
                    'timestamp': timestamp_dt,
                    'portfolio_value': self.backtesting_service.current_balance + (self.backtesting_service.btc_balance * current_price)
                }
                
            elif signal == 'SELL' and self.backtesting_service.btc_balance > 0:
                # Simular venta
                btc_amount = self.backtesting_service.btc_balance
                usd_received = btc_amount * current_price
                
                # Actualizar balances
                self.backtesting_service.current_balance += usd_received
                self.backtesting_service.btc_balance = 0
                
                # Registrar trade
                trade = {
                    'type': 'SELL',
                    'price': current_price,
                    'amount': btc_amount,
                    'timestamp': timestamp_dt,
                    'confidence': confidence,
                    'predicted_price': predicted_price
                }
                self.backtesting_service.manual_trades.append(trade)
                
                logger.info(f"VENTA EJECUTADA: {btc_amount:.6f} BTC a ${current_price:,.2f} - Balance USD: ${self.backtesting_service.current_balance:,.2f}")
                
                return {
                    'action': 'sell',
                    'price': current_price,
                    'amount': btc_amount,
                    'timestamp': timestamp_dt,
                    'portfolio_value': self.backtesting_service.current_balance
                }
            elif signal == 'BUY':
                logger.info(f"COMPRA RECHAZADA: Balance insuficiente ${self.backtesting_service.current_balance:,.2f} < $100")
            elif signal == 'SELL':
                logger.info(f"VENTA RECHAZADA: Sin BTC para vender (Balance BTC: {self.backtesting_service.btc_balance:.6f})")
            else:
                logger.info(f"HOLD: Senal {signal} - Balance USD: ${self.backtesting_service.current_balance:,.2f}, BTC: {self.backtesting_service.btc_balance:.6f}")
            
            # HOLD o condiciones no cumplidas
            portfolio_value = self.backtesting_service.current_balance + (self.backtesting_service.btc_balance * current_price)
            return {
                'action': 'hold',
                'price': current_price,
                'timestamp': timestamp_dt,
                'portfolio_value': portfolio_value
            }
            
        except Exception as e:
            logger.error(f"Error en ejecución manual de trade: {e}")
            return {
                'action': 'error',
                'price': current_price,
                'timestamp': timestamp,
                'portfolio_value': self.backtesting_service.current_balance
            }
    
    def _generate_visualization(self, historical_data: pd.DataFrame, predictions: List[Dict], metrics: Dict[str, Any]):
        """Generar visualización del backtesting con manejo de errores."""
        try:
            visualizer = BacktestingVisualizer()
            
            # Crear la gráfica con manejo de errores
            try:
                fig = visualizer.create_backtesting_chart(
                    historical_data=historical_data,
                    predictions_data=predictions,
                    trades_data=self.backtesting_service.manual_trades,
                    metrics=metrics
                )
                
                # Guardar la gráfica
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                chart_filename = f"backtesting_chart_{timestamp}.html"
                chart_path = visualizer.save_chart(
                    fig=fig,
                    filename=chart_filename,
                    historical_data=historical_data,
                    predictions_data=predictions,
                    trades_data=self.backtesting_service.manual_trades,
                    metrics=metrics
                )
                
                logger.info(f"✅ Gráfica guardada exitosamente en: {chart_path}")
                
                # Mostrar en navegador (opcional)
                # visualizer.show_chart(fig)
                
            except Exception as e:
                logger.error(f"❌ Error al generar gráfica con Plotly: {e}")
                logger.info("🔄 Intentando con matplotlib como alternativa...")
                
                # Usar matplotlib como alternativa
                chart_path = visualizer.save_matplotlib_chart(
                    filepath=os.path.join("charts", f"backtesting_chart_{timestamp}_fallback.html"),
                    historical_data=historical_data,
                    predictions_data=predictions,
                    trades_data=self.backtesting_service.manual_trades,
                    metrics=metrics
                )
                logger.info(f"✅ Gráfica alternativa guardada en: {chart_path}")
                
        except Exception as e:
            logger.error(f"❌ Error crítico al generar visualización: {e}")
            logger.error("No se pudo generar ninguna visualización. Por favor revise los logs para más detalles.")
            
            # Intentar guardar al menos un archivo de registro con los datos
            try:
                error_log = {
                    'error': str(e),
                    'timestamp': datetime.now().isoformat(),
                    'metrics': metrics,
                    'trades_count': len(self.backtesting_service.manual_trades) if hasattr(self.backtesting_service, 'manual_trades') else 0,
                    'predictions_count': len(predictions) if predictions else 0
                }
                
                error_file = os.path.join("logs", f"backtesting_error_{int(datetime.now().timestamp())}.json")
                import json
                with open(error_file, 'w') as f:
                    json.dump(error_log, f, indent=2)
                logger.info(f"Se ha guardado un registro de error en: {error_file}")
            except Exception as inner_e:
                logger.error(f"No se pudo guardar el registro de error: {inner_e}")

    def print_results(self, metrics: Dict):
        """Imprimir resultados del backtesting de forma legible."""
        if not metrics:
            logger.error("❌ No hay métricas para mostrar")
            return
        
        print("\n" + "=" * 80)
        print("🎯 RESULTADOS DEL BACKTESTING - MODELO LSTM")
        print("=" * 80)
        
        # Métricas de predicción
        print("\n📊 ESTADÍSTICAS DE PREDICCIÓN:")
        print(f"   🔮 Total predicciones: {metrics.get('total_predictions', 0)}")
        print(f"   ✅ Predicciones exitosas: {metrics.get('successful_predictions', 0)}")
        print(f"   ❌ Predicciones fallidas: {metrics.get('failed_predictions', 0)}")
        print(f"   📈 Tasa de éxito: {metrics.get('prediction_success_rate', 0):.2f}%")
        
        # Métricas de trading
        print("\n💰 RENDIMIENTO DE TRADING:")
        print(f"   💵 Balance inicial: ${self.initial_balance:,.2f}")
        print(f"   💎 Valor final del portfolio: ${metrics.get('current_portfolio_value', 0):,.2f}")
        print(f"   📈 Retorno total: {metrics.get('total_return_pct', 0):.2f}%")
        print(f"   💸 P&L total: ${metrics.get('total_profit_loss', 0):,.2f}")
        print(f"   💰 Balance USD: ${self.backtesting_service.current_balance:,.2f}")
        print(f"   ₿ Balance BTC: {self.backtesting_service.btc_balance:.6f}")
        
        # Métricas de operaciones
        print("\n🔄 ESTADÍSTICAS DE OPERACIONES:")
        print(f"   📊 Total operaciones: {metrics.get('total_trades', 0)}")
        print(f"   🔄 Operaciones completadas: {metrics.get('completed_trades', 0)}")
        print(f"   ✅ Operaciones ganadoras: {metrics.get('winning_trades', 0)}")
        print(f"   ❌ Operaciones perdedoras: {metrics.get('losing_trades', 0)}")
        print(f"   🎯 Tasa de acierto: {metrics.get('win_rate', 0):.2f}%")
        print(f"   💰 Ganancia promedio por operación: ${metrics.get('avg_profit_per_trade', 0):.2f}")
        
        # Métricas avanzadas (usando valores por defecto si no están disponibles)
        print("\n📈 MÉTRICAS AVANZADAS:")
        print(f"   📊 Ratio de Sharpe: {metrics.get('sharpe_ratio', 0):.3f}")
        print(f"   📉 Drawdown máximo: {metrics.get('max_drawdown', 0):.2f}%")
        print(f"   🌊 Volatilidad: {metrics.get('volatility', 0):.2f}%")
        print(f"   ⚖️ Factor de beneficio: {metrics.get('profit_factor', 0):.2f}")
        
        # Evaluación del rendimiento
        print("\n🎯 EVALUACIÓN DEL MODELO:")
        return_pct = metrics.get('total_return_pct', 0)
        sharpe = metrics.get('sharpe_ratio', 0)
        win_rate = metrics.get('win_rate', 0)
        
        if return_pct > 10 and sharpe > 1.0 and win_rate > 55:
            print("   🟢 EXCELENTE: Modelo altamente rentable y estable")
        elif return_pct > 5 and sharpe > 0.5 and win_rate > 50:
            print("   🟡 BUENO: Modelo rentable con gestión de riesgo")
        elif return_pct > 0:
            print("   🟠 ACEPTABLE: Modelo ligeramente rentable")
        else:
            print("   🔴 MEJORABLE: Modelo requiere optimización")
        
        print("\n💡 RECOMENDACIONES:")
        if sharpe < 0.5:
            print("   ⚠️ Considerar ajustar parámetros de confianza mínima")
        if metrics.get('max_drawdown', 0) > 20:
            print("   ⚠️ Implementar stop-loss más estricto")
        if win_rate < 50:
            print("   ⚠️ Revisar umbrales de señales de trading")
        
        print("\n" + "=" * 80)

def main():
    """Función principal del backtesting."""
    print("🚀 BACKTESTING DEL MODELO LSTM - PREDICCIÓN DE PRECIOS BTC")
    print("=" * 70)
    
    # Configuración
    model_path = "models/btc_lstm_model.h5"
    initial_balance = 10000.0  # $10,000 USD
    days_to_test = 15  # Últimos 15 días
    
    # Crear backtester
    backtester = ModelBacktester(model_path, initial_balance)
    
    # Cargar modelo
    if not backtester.load_model():
        print("❌ No se pudo cargar el modelo. Ejecuta train_and_save_model.py primero.")
        return
    
    # Obtener datos históricos
    print(f"\n📊 Obteniendo datos de los últimos {days_to_test} días...")
    historical_data = backtester.get_historical_data(days=days_to_test)
    
    if historical_data.empty:
        print("❌ No se pudieron obtener datos históricos")
        return
    
    # Ejecutar backtesting
    print(f"\n🔄 Ejecutando backtesting con {len(historical_data)} registros...")
    metrics = backtester.run_backtesting(historical_data)
    
    # Mostrar resultados
    backtester.print_results(metrics)
    
    # Guardar resultados
    try:
        results_file = f"logs/backtesting_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(results_file, 'w') as f:
            f.write(f"Backtesting Results - {datetime.now()}\n")
            f.write("=" * 50 + "\n")
            for key, value in metrics.items():
                f.write(f"{key}: {value}\n")
        print(f"\n💾 Resultados guardados en: {results_file}")
    except Exception as e:
        logger.warning(f"No se pudieron guardar los resultados: {e}")

if __name__ == "__main__":
    main()
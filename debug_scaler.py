#!/usr/bin/env python3
"""
Script de diagnóstico para el problema del scaler
"""

import sys
import os
import numpy as np
import pandas as pd
from pathlib import Path

# Agregar src al path
src_path = Path(__file__).parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

# Importaciones con manejo de errores
try:
    from src.ai.predictors.price_predictor import PricePredictor
    from src.services.binance_service import BinanceService
except ImportError:
    # Fallback para importaciones directas
    sys.path.insert(0, str(Path(__file__).parent))
    from src.ai.predictors.price_predictor import PricePredictor
    from src.services.binance_service import BinanceService

def main():
    print("🔍 DIAGNÓSTICO DEL PROBLEMA DEL SCALER")
    print("=" * 50)
    
    # 1. Cargar datos reales
    print("1. Obteniendo datos de Binance...")
    binance = BinanceService()
    df = binance.get_klines_data('BTCUSDT', '15m', 100)  # Solo 100 registros para prueba
    
    current_price = df['close'].iloc[-1]
    print(f"   💰 Precio actual: ${current_price:,.2f}")
    print(f"   📊 Rango de precios: ${df['close'].min():,.2f} - ${df['close'].max():,.2f}")
    
    # 2. Cargar modelo entrenado
    print("\n2. Cargando modelo entrenado...")
    predictor = PricePredictor()
    model_path = "models/btc_lstm_model.h5"
    
    if os.path.exists(model_path):
        predictor.load_model(model_path)
        print("   ✅ Modelo cargado exitosamente")
    else:
        print("   ❌ Modelo no encontrado")
        return
    
    # 3. Inspeccionar scaler directamente
    print("\n3. Inspeccionando scaler del modelo...")
    scaler = predictor.lstm_model.scaler
    
    print(f"   🔍 Tipo de scaler: {type(scaler).__name__}")
    print(f"   📊 Data min: {getattr(scaler, 'data_min_', 'N/A')}")
    print(f"   📊 Data max: {getattr(scaler, 'data_max_', 'N/A')}")
    print(f"   📊 Scale: {getattr(scaler, 'scale_', 'N/A')}")
    print(f"   📊 Min: {getattr(scaler, 'min_', 'N/A')}")
    
    # 4. Probar normalización y desnormalización manual
    print("\n4. Probando normalización manual...")
    test_prices = [current_price, current_price * 0.9, current_price * 1.1]
    
    for price in test_prices:
        # Normalizar
        normalized = scaler.transform([[price]])[0][0]
        # Desnormalizar
        denormalized = scaler.inverse_transform([[normalized]])[0][0]
        
        print(f"   💰 Precio: ${price:,.2f} → Normalizado: {normalized:.6f} → Desnormalizado: ${denormalized:,.2f}")
        
        if abs(price - denormalized) > 1:
            print(f"   ⚠️  ERROR: Diferencia de ${abs(price - denormalized):,.2f}")
    
    # 5. Hacer una predicción y diagnosticar
    print("\n5. Haciendo predicción con diagnóstico...")
    try:
        predicted_price, confidence = predictor.predict_next_price(df)
        print(f"   🎯 Predicción: ${predicted_price:,.2f}")
        print(f"   🎯 Precio actual: ${current_price:,.2f}")
        print(f"   📊 Diferencia: ${abs(predicted_price - current_price):,.2f}")
        print(f"   📊 Cambio %: {((predicted_price - current_price) / current_price) * 100:.2f}%")
        print(f"   🎯 Confianza: {confidence:.2f}")
        
        if abs(predicted_price - current_price) > current_price * 0.1:  # Más del 10%
            print("   ❌ PROBLEMA: Predicción muy alejada del precio real")
        else:
            print("   ✅ Predicción dentro del rango razonable")
            
    except Exception as e:
        print(f"   ❌ Error en predicción: {e}")
    
    print("\n" + "=" * 50)
    print("🔍 Diagnóstico completado")

if __name__ == "__main__":
    main()
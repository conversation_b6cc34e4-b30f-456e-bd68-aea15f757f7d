# Configuración del Dashboard BTC
# Copia este archivo a .env y ajusta los valores según tu entorno

# Configuración de Binance (opcional - para datos públicos no se necesitan)
# BINANCE_API_KEY=tu_api_key_aqui
# BINANCE_API_SECRET=tu_api_secret_aqui

# Configuración del servidor
DASHBOARD_HOST=0.0.0.0
DASHBOARD_PORT=8050
DASHBOARD_DEBUG=true

# Configuración de logging
LOG_LEVEL=INFO
LOG_FILE=logs/dashboard.log

# Configuración de actualización
UPDATE_INTERVAL_MS=900000

# Configuración de caché
CACHE_ENABLED=true
CACHE_TTL=300

# Configuración de seguridad
CSRF_PROTECTION=true
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

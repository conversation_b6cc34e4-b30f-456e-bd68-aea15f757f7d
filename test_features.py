#!/usr/bin/env python3
"""
Script para probar la discrepancia de características
"""

import sys
import os
sys.path.append('src')

from src.ai.predictors.price_predictor import PricePredictor
from src.services.binance_service import BinanceService
import pandas as pd
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')

def main():
    print("🔍 Probando discrepancia de características...")
    
    # Crear servicios
    binance_service = BinanceService()
    predictor = PricePredictor()
    
    # Cargar modelo
    model_path = "models/btc_lstm_model.h5"
    if os.path.exists(model_path):
        predictor.load_model(model_path)
        print(f"✅ Modelo cargado desde: {model_path}")
    else:
        print(f"❌ No se encontró el modelo en: {model_path}")
        return
    
    # Obtener datos
    print("📊 Obteniendo datos de Binance...")
    df = binance_service.get_klines_data(limit=100)
    
    if df.empty:
        print("❌ No se pudieron obtener datos")
        return
        
    print(f"✅ Datos obtenidos: {len(df)} registros")
    print(f"📋 Columnas de datos: {list(df.columns)}")
    
    # Generar señales (esto debería mostrar el logging detallado)
    print("\n🎯 Generando señales de trading...")
    try:
        signals = predictor.generate_trading_signals(df)
        print(f"✅ Señales generadas: {signals}")
    except Exception as e:
        print(f"❌ Error generando señales: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
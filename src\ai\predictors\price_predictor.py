"""Servicio de predicción de precios usando IA"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from datetime import datetime, timedelta
import os
import pickle

from ..indicators.technical_indicators import TechnicalIndicators
from ..models.lstm_model import LSTMModel
from ...services.data_processor import DataProcessor
from ...utils.prediction_logger import PredictionLogger

# Importar configuración
try:
    import sys
    from pathlib import Path
    # Agregar src al path si no está
    src_path = Path(__file__).parent.parent.parent
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))
    from config.settings import AI_CONFIG
except ImportError:
    # Fallback si no se puede importar
    AI_CONFIG = {"sequence_length": 30}

logger = logging.getLogger(__name__)

class PricePredictor:
    """Servicio principal para predicción de precios con IA"""
    
    def __init__(self, model_path: Optional[str] = None):
        """
        Inicializar predictor de precios
        
        Args:
            model_path: Ruta al modelo pre-entrenado (opcional)
        """
        self.technical_indicators = TechnicalIndicators()
        self.lstm_model = None  # Se inicializará cuando se entrene o cargue
        self.is_ready = False
        self.last_prediction = None
        self.prediction_confidence = 0.0
        
        # Servicios separados
        self.data_processor = DataProcessor()
        self.prediction_logger = PredictionLogger()
        
        # Scalers para normalización
        self.feature_scaler = None
        self.target_scaler = None
        
        self._setup_logging()
        
        # Cargar modelo existente si se especifica
        if model_path and os.path.exists(model_path):
            self.load_model(model_path)
        else:
            logger.warning("No se especificó modelo. Usar load_model() para cargar uno existente.")
    
    def load_model(self, model_path: str):
        """Carga el modelo y los scalers."""
        try:
            self.lstm_model = LSTMModel()
            self.lstm_model.load_model(model_path)
            
            # Cargar scalers asociados - CORREGIDO para usar 'price_scaler'
            scalers_path = model_path.replace('.h5', '_scalers.pkl')
            if os.path.exists(scalers_path):
                with open(scalers_path, 'rb') as f:
                    scalers = pickle.load(f)
                self.feature_scaler = scalers.get('feature_scaler')
                # Usar target_scaler para el target ATR-escalado
                self.target_scaler = scalers.get('target_scaler')
                
                # Asignar scalers al modelo LSTM también
                self.lstm_model.feature_scaler = self.feature_scaler
                self.lstm_model.scaler = self.target_scaler
                
                if self.feature_scaler is None or self.target_scaler is None:
                    logger.warning(f"No se encontraron los scalers en {scalers_path}.")
                    logger.warning(f"Claves disponibles: {list(scalers.keys())}")
                else:
                    logger.info(f"Scalers cargados desde {scalers_path}")
            else:
                logger.warning(f"Archivo de scalers no encontrado en {scalers_path}.")
            
            self.is_ready = True
            logger.info(f"Modelo y scalers cargados desde {model_path}")
        except Exception as e:
            logger.error(f"Error al cargar el modelo: {e}")
            self.is_ready = False
    
    def _setup_logging(self):
        """Configurar logging"""
        logger.setLevel(logging.INFO)
    
    def prepare_data_for_prediction(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Preparar datos para predicción agregando indicadores técnicos y validación
        
        Args:
            df: DataFrame con datos OHLCV
            
        Returns:
            DataFrame con indicadores técnicos agregados y validado
        """
        try:
            logger.info(f"Preparando datos para predicción: {len(df)} filas")
            
            # Verificar columnas requeridas
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                raise ValueError(f"Faltan columnas requeridas: {missing_columns}")
            
            # Crear copia para no modificar el original
            prepared_df = df.copy()
            
            # Validar y limpiar datos usando DataProcessor
            prepared_df = self.data_processor.validate_and_clean_data(prepared_df)
            
            # Calcular indicadores técnicos
            df_with_indicators = self.technical_indicators.calculate_all_indicators(prepared_df)
            
            # Obtener y validar las columnas en orden consistente
            target_columns = self.technical_indicators.get_feature_columns()
            
            # Verificar que todas las columnas requeridas estén presentes
            missing_cols = set(target_columns) - set(df_with_indicators.columns)
            if missing_cols:
                logger.warning(f"Columnas faltantes en datos: {missing_cols}")
                # Crear columnas faltantes con valores por defecto
                for col in missing_cols:
                    df_with_indicators[col] = 0.0
                logger.info("Columnas faltantes creadas con valores por defecto")
                
            # Crear DataFrame manteniendo el orden exacto de columnas
            final_data = df_with_indicators[target_columns].copy()
            
            # Validación básica final usando DataProcessor
            final_data = self.data_processor.basic_validation(final_data)
            
            if len(final_data) == 0:
                raise ValueError("No quedan datos válidos después de preparación")
            
            logger.info(f"Datos preparados: {len(final_data)} filas, {len(final_data.columns)} características")
            return final_data
            
        except Exception as e:
            logger.error(f"Error preparando datos: {e}")
            raise
    
    def train_model(self, df: pd.DataFrame, 
                   target_column: str = 'close',
                   epochs: int = 30,  # Reducido para predicciones más conservadoras
                   save_model: bool = True) -> Dict[str, Any]:
        """
        Entrenar modelo LSTM
        
        Args:
            df: DataFrame con datos históricos
            target_column: Columna objetivo
            epochs: Número de épocas
            save_model: Si guardar el modelo entrenado
            
        Returns:
            Historial de entrenamiento
        """
        try:
            logger.info("Iniciando entrenamiento del modelo...")
            
            # Preparar datos
            prepared_df = self.prepare_data_for_prediction(df)
            
            # Crear modelo LSTM si no existe
            if self.lstm_model is None:
                logger.info("Creando nuevo modelo LSTM...")
                self.lstm_model = LSTMModel()
            
            # Entrenar modelo
            history = self.lstm_model.train(
                prepared_df, 
                target_column=target_column,
                epochs=epochs,
                validation_split=0.2
            )
            
            # Copiar scalers del modelo entrenado
            self.feature_scaler = self.lstm_model.feature_scaler
            self.target_scaler = self.lstm_model.scaler  # price_scaler
            
            # Guardar modelo si se solicita
            if save_model:
                model_path = "models/btc_lstm_model.h5"
                os.makedirs("models", exist_ok=True)
                self.lstm_model.save_model(model_path)
                logger.info(f"Modelo y scalers guardados en: {model_path}")
            
            self.is_ready = True
            logger.info("Modelo entrenado y listo para predicciones")
            
            return history
            
        except Exception as e:
            logger.error(f"Error entrenando modelo: {e}")
            raise
    
    def predict_next_price(self, df: pd.DataFrame, target_column: str = 'close') -> Tuple[float, float]:
        """Predicción con logging usando PredictionLogger"""
        try:
            current_price = df[target_column].iloc[-1]
            self.prediction_logger.log_input_data(current_price, len(df))
            
            # Verificar que existe ATR
            if 'atr' not in df.columns:
                raise ValueError("Columna 'atr' no encontrada. Se requiere ATR para desescalar predicciones")
            
            current_atr = df['atr'].iloc[-1]
            
            # Preparar características
            features = self._prepare_features_for_keras(df)
            if features is None:
                raise ValueError("No se pudieron preparar las características")
            
            # Hacer predicción usando el nuevo método con ATR
            predicted_price = self.lstm_model.predict_from_features(
                features, current_price=current_price, current_atr=current_atr
            )
            self.prediction_logger.log_final_prediction(predicted_price)
            
            # Calcular confianza
            confidence = self._calculate_prediction_confidence(df)
            
            change_pct = ((predicted_price - current_price) / current_price) * 100
            
            # Log del resultado
            self.prediction_logger.log_prediction_result(
                current_price, predicted_price, change_pct, confidence
            )
            
            # Validaciones usando PredictionLogger
            if abs(change_pct) > 10:
                self.prediction_logger.log_high_volatility_warning(
                    change_pct, current_price, predicted_price
                )
            
            if confidence < 0.3:
                volatility = df[target_column].pct_change().std() * 100
                self.prediction_logger.log_low_confidence_warning(confidence, volatility)
            
            return predicted_price, confidence
            
        except Exception as e:
            self.prediction_logger.log_prediction_error(e)
            raise
    
    def predict_price_sequence(self, df: pd.DataFrame, 
                             target_column: str = 'close',
                             steps: int = 5) -> Tuple[np.ndarray, float]:
        """
        Predecir secuencia de precios futuros
        
        Args:
            df: DataFrame con datos actuale Xa  s
            target_column: Columna objetivo
            steps: Número de pasos a predecir
            
        Returns:
            Tupla con (array_predicciones, confianza_promedio)
        """
        try:
            if not self.is_ready:
                raise ValueError("El modelo debe estar entrenado antes de hacer predicciones")
            
            # Preparar datos
            prepared_df = self.prepare_data_for_prediction(df)
            
            # Hacer predicciones
            predictions = self.lstm_model.predict_sequence(prepared_df, target_column, steps)
            
            # Calcular confianza promedio
            confidence = self._calculate_prediction_confidence(df)
            
            logger.info(f"Secuencia de {steps} predicciones completada")
            return predictions, confidence
            
        except Exception as e:
            logger.error(f"Error en predicción de secuencia: {e}")
            raise
    
    def generate_trading_signals(self, df: pd.DataFrame, 
                                target_column: str = 'close',
                                confidence_threshold: float = 0.5) -> Dict[str, Any]:
        """
        Generar señales de trading basadas en predicciones
        
        Args:
            df: DataFrame con datos actuales
            target_column: Columna objetivo
            confidence_threshold: Umbral de confianza mínimo
            
        Returns:
            Diccionario con señales de trading
        """
        try:
            logger.info("Generando señales de trading...")
            
            # Obtener precio actual y predicción
            current_price = df[target_column].iloc[-1]
            predicted_price, confidence = self.predict_next_price(df, target_column)
            
            # Calcular cambio porcentual esperado
            price_change_percent = ((predicted_price - current_price) / current_price) * 100
            
            # Generar señal basada en predicción y confianza
            signal = self._determine_trading_signal(
                current_price, predicted_price, confidence, confidence_threshold
            )
            
            # Calcular métricas adicionales
            risk_reward_ratio = self._calculate_risk_reward_ratio(df, predicted_price)
            
            trading_signals = {
                'signal': signal,
                'current_price': current_price,
                'predicted_price': predicted_price,
                'confidence': confidence,
                'price_change_percent': price_change_percent,
                'risk_reward_ratio': risk_reward_ratio,
                'timestamp': datetime.now(),
                'recommendation': self._generate_recommendation(signal, confidence, price_change_percent)
            }
            
            logger.info(f"Señal generada: {signal} (Confianza: {confidence * 100:.2f}%)")
            return trading_signals
            
        except Exception as e:
            logger.error(f"Error generando señales de trading: {e}")
            raise
    
    def _calculate_prediction_confidence(self, df: pd.DataFrame) -> float:
        """Calcular confianza de la predicción basada en volatilidad"""
        try:
            # Calcular volatilidad reciente (últimos 20 períodos)
            recent_prices = df['close'].tail(20)
            returns = recent_prices.pct_change().dropna()
            
            # Volatilidad como inverso de la confianza
            volatility = returns.std()
            
            # Convertir volatilidad a confianza (0-1)
            # Menor volatilidad = mayor confianza
            confidence = max(0.1, min(0.95, 1 - (volatility * 10)))
            
            return confidence
            
        except Exception as e:
            logger.error(f"Error calculando confianza: {e}")
            return 0.5
    
    def _determine_trading_signal(self, current_price: float, 
                                 predicted_price: float, 
                                 confidence: float, 
                                 threshold: float) -> str:
        """Determinar señal de trading optimizada para oportunidades ±2%"""
        if confidence < threshold:
            return "ESPERA"
        
        price_change = predicted_price - current_price
        change_percent = (price_change / current_price) * 100
        
        # OPTIMIZADO PARA DETECTAR OPORTUNIDADES DE ±2% CON MAYOR PRECISIÓN
        if change_percent >= 2.0:  # ≥2% LONG - Señal fuerte de compra
            if confidence >= 0.7:
                return "LONG_FUERTE"  # Alta confianza + ≥2% = LONG fuerte
            else:
                return "LONG"  # Confianza media + ≥2% = LONG moderado
        elif change_percent >= 1.2:  # 1.2-2% - Oportunidad LONG moderada
            if confidence >= 0.6:
                return "LONG_MODERADO"
            else:
                return "ESPERA"  # Confianza baja, esperar
        elif change_percent <= -2.0:  # ≤-2% SHORT - Señal fuerte de venta
            if confidence >= 0.7:
                return "SHORT_FUERTE"  # Alta confianza + ≤-2% = SHORT fuerte
            else:
                return "SHORT"  # Confianza media + ≤-2% = SHORT moderado
        elif change_percent <= -1.2:  # -1.2 a -2% - Oportunidad SHORT moderada
            if confidence >= 0.6:
                return "SHORT_MODERADO"
            else:
                return "ESPERA"  # Confianza baja, esperar
        else:
            return "MANTENER"  # Cambio <±1.2% - No hay oportunidad clara
    
    def _calculate_risk_reward_ratio(self, df: pd.DataFrame, predicted_price: float) -> float:
        """Calcular ratio riesgo/beneficio"""
        try:
            current_price = df['close'].iloc[-1]
            
            # Calcular ATR para stop loss
            atr = df['atr'].iloc[-1] if 'atr' in df.columns else df['close'].iloc[-1] * 0.02
            
            # Stop loss a 2 ATR
            stop_loss = current_price - (2 * atr)
            
            # Beneficio potencial
            potential_profit = predicted_price - current_price
            
            # Riesgo potencial
            potential_risk = current_price - stop_loss
            
            if potential_risk > 0:
                return potential_profit / potential_risk
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"Error calculando ratio riesgo/beneficio: {e}")
            return 0.0
    
    def _generate_recommendation(self, signal: str, confidence: float, 
                                price_change_percent: float) -> str:
        """Generar recomendación de trading optimizada para LONG/SHORT"""
        if signal == "ESPERA":
            return "⏳ ESPERAR: Confianza insuficiente o cambio <±1.2% - Aguardar mejor oportunidad"
        elif signal == "LONG_FUERTE":
            return f"🚀 LONG FUERTE: +{price_change_percent:.2f}% (Conf: {confidence:.1%}) - ¡ENTRADA AGRESIVA!"
        elif signal == "LONG":
            return f"📈 LONG: +{price_change_percent:.2f}% (Conf: {confidence:.1%}) - Entrada moderada recomendada"
        elif signal == "LONG_MODERADO":
            return f"📊 LONG MODERADO: +{price_change_percent:.2f}% (Conf: {confidence:.1%}) - Considerar entrada gradual"
        elif signal == "SHORT_FUERTE":
            return f"🔻 SHORT FUERTE: {price_change_percent:.2f}% (Conf: {confidence:.1%}) - ¡VENTA AGRESIVA!"
        elif signal == "SHORT":
            return f"⚠️ SHORT: {price_change_percent:.2f}% (Conf: {confidence:.1%}) - Venta moderada recomendada"
        elif signal == "SHORT_MODERADO":
            return f"📉 SHORT MODERADO: {price_change_percent:.2f}% (Conf: {confidence:.1%}) - Considerar venta gradual"
        else:
            return f"⏸️ MANTENER: {price_change_percent:+.2f}% - Sin oportunidad clara (±2% objetivo)"
    
    def evaluate_model_performance(self, df: pd.DataFrame, 
                                 target_column: str = 'close') -> Dict[str, float]:
        """Evaluar rendimiento del modelo"""
        try:
            if not self.is_ready:
                raise ValueError("El modelo debe estar entrenado antes de evaluar")
            
            prepared_df = self.prepare_data_for_prediction(df)
            metrics = self.lstm_model.evaluate(prepared_df, target_column)
            
            logger.info(f"Rendimiento del modelo: {metrics}")
            return metrics
            
        except Exception as e:
            logger.error(f"Error evaluando rendimiento: {e}")
            raise
    

    
    def _prepare_features_for_keras(self, df: pd.DataFrame) -> np.ndarray:
        """Preparar características para el modelo Keras - CON COMPATIBILIDAD GARANTIZADA"""
        try:
            # Calcular indicadores técnicos
            df_with_indicators = self.technical_indicators.calculate_all_indicators(df)
            
            # Obtener EXACTAMENTE las columnas que necesita el modelo (como se entrenó)
            target_columns = self.technical_indicators.get_feature_columns()
            
            # Asegurar que todas las columnas objetivo existan
            for col in target_columns:
                if col not in df_with_indicators.columns:
                    # Agregar columna faltante con valores por defecto
                    if col in ['open', 'high', 'low', 'close']:
                        df_with_indicators[col] = df_with_indicators['close'].fillna(0)
                    elif col in ['volume', 'quote_volume', 'trades']:
                        df_with_indicators[col] = df_with_indicators.get('volume', 0)
                    elif 'rsi' in col or 'stoch' in col:
                        df_with_indicators[col] = 50
                    elif 'williams' in col:
                        df_with_indicators[col] = -50
                    elif any(x in col for x in ['macd', 'cci']):
                        df_with_indicators[col] = 0
                    elif 'adx' in col:
                        df_with_indicators[col] = 25
                    elif 'bb_percent' in col:
                        df_with_indicators[col] = 0.5
                    elif any(x in col for x in ['upper', 'resistance']):
                        df_with_indicators[col] = df_with_indicators['high'].fillna(df_with_indicators['close']) * 1.02
                    elif any(x in col for x in ['lower', 'support']):
                        df_with_indicators[col] = df_with_indicators['low'].fillna(df_with_indicators['close']) * 0.98
                    else:
                        df_with_indicators[col] = df_with_indicators['close'].fillna(0)
                    
                    logger.debug(f"Columna faltante agregada: {col}")
            
            # Seleccionar EXACTAMENTE las columnas objetivo en el ORDEN CORRECTO
            feature_data = df_with_indicators[target_columns].copy()
            
            # DEBUG: Verificar características generadas
            logger.info(f"ANALISIS DE COLUMNAS:")
            logger.info(f"   Total columnas generadas: {len(df_with_indicators.columns)}")
            logger.info(f"   Columnas objetivo (modelo): {len(target_columns)}")
            logger.info(f"   Features finales seleccionadas: {len(feature_data.columns)}")
            logger.info(f"   Columnas generadas: {sorted(df_with_indicators.columns.tolist())}")
            logger.info(f"   Columnas objetivo: {target_columns}")
            logger.info(f"   Columnas finales: {list(feature_data.columns)}")
            
            # Identificar columnas extra
            extra_cols = set(df_with_indicators.columns) - set(target_columns)
            if extra_cols:
                logger.info(f"   ⚠️ Columnas EXTRA generadas: {sorted(extra_cols)}")
            
            # Verificar compatibilidad con scaler
            if self.feature_scaler:
                expected_features = self.feature_scaler.n_features_in_
                actual_features = len(feature_data.columns)
                logger.info(f"Scaler espera: {expected_features} características")
                logger.info(f"Tenemos: {actual_features} características")

                if actual_features != expected_features:
                    logger.warning(f"🔧 SINCRONIZANDO CARACTERÍSTICAS: {actual_features} → {expected_features}")
                    
                    if actual_features > expected_features:
                        # Eliminar columnas extras manteniendo las primeras (más importantes)
                        logger.info(f"   ✂️  Eliminando {actual_features - expected_features} columnas extras...")
                        feature_data = feature_data.iloc[:, :expected_features]
                        
                    elif actual_features < expected_features:
                        # Rellenar características faltantes con valores neutros
                        missing_count = expected_features - actual_features
                        logger.info(f"   ➕ Agregando {missing_count} características faltantes...")
                        
                        # Usar la media de las características existentes como valor neutro
                        mean_values = feature_data.mean(axis=1)
                        for i in range(missing_count):
                            feature_data[f"pad_{i}"] = mean_values
                    
                    # VERIFICACIÓN FINAL OBLIGATORIA
                    final_count = len(feature_data.columns)
                    if final_count == expected_features:
                        logger.info(f"   ✅ SINCRONIZACIÓN EXITOSA: {final_count} características")
                    else:
                        # Forzar sincronización exacta
                        logger.error(f"   🚨 FORZANDO SINCRONIZACIÓN: {final_count} → {expected_features}")
                        if final_count > expected_features:
                            feature_data = feature_data.iloc[:, :expected_features]
                        else:
                            # Duplicar la última columna hasta completar
                            last_col = feature_data.iloc[:, -1]
                            for i in range(final_count, expected_features):
                                feature_data[f"dup_{i}"] = last_col
                        
                        logger.warning(f"   ⚠️  SINCRONIZACIÓN FORZADA: {len(feature_data.columns)} características finales")
                else:
                    logger.info(f"   ✅ CARACTERÍSTICAS PERFECTAMENTE SINCRONIZADAS: {actual_features}")
            
            # Crear secuencia de características (flexible con datos disponibles)
            sequence_length = AI_CONFIG.get("sequence_length", 30)
            min_sequence_length = 30  # Mínimo aceptable
            
            if len(feature_data) < min_sequence_length:
                logger.warning(f"Datos insuficientes: {len(feature_data)} < {min_sequence_length}. Predicción no disponible.")
                raise ValueError(f"Se requieren al menos {min_sequence_length} períodos de datos")
            
            # Usar los datos disponibles (mínimo 30, ideal 60)
            actual_sequence_length = min(sequence_length, len(feature_data))
            recent_data = feature_data.tail(actual_sequence_length).values
            
            # Si tenemos menos de 60, rellenar al inicio con el primer valor disponible
            if actual_sequence_length < sequence_length:
                padding_needed = sequence_length - actual_sequence_length
                first_row = recent_data[0:1]  # Primer fila
                padding = np.repeat(first_row, padding_needed, axis=0)
                recent_data = np.vstack([padding, recent_data])
                logger.debug(f"Datos rellenados: {actual_sequence_length} -> {sequence_length} períodos")
            
            # Asegurar que no hay valores NaN o infinitos
            recent_data = np.nan_to_num(recent_data, nan=0.0, posinf=1e6, neginf=-1e6)
            
            # DEBUG: Verificar datos antes de normalizar
            logger.debug(f"Shape datos: {recent_data.shape}")
            logger.debug(f"Rango datos: {recent_data.min():.4f} a {recent_data.max():.4f}")
            
            # Normalizar características - USAR NUMPY ARRAY PARA EVITAR WARNING
            if self.feature_scaler:
                # Asegurar que recent_data es numpy array para evitar warning de feature names
                if not isinstance(recent_data, np.ndarray):
                    recent_data = np.array(recent_data)
                
                # Suprimir warnings de sklearn sobre feature names
                import warnings
                with warnings.catch_warnings():
                    warnings.filterwarnings("ignore", message="X does not have valid feature names")
                    recent_data = self.feature_scaler.transform(recent_data)
                logger.debug(f"Datos normalizados: {recent_data.shape}")
                
                # DEBUG: Verificar rango después de normalización con RobustScaler
                logger.debug(f"Rango después de RobustScaler: {recent_data.min():.6f} a {recent_data.max():.6f}")
                
                # NOTA: RobustScaler maneja automáticamente valores extremos usando mediana y IQR
                # No necesitamos clipping manual que destruye información válida
            
            # Reshape para LSTM: (1, sequence_length, features)
            features = recent_data.reshape(1, sequence_length, -1)
            logger.debug(f"Features finales: {features.shape}")
            
            return features
            
        except Exception as e:
            logger.error(f"Error preparando características para Keras: {e}")
            raise
    
    def predict_sequence(self, df: pd.DataFrame, 
                        target_column: str = 'close',
                        steps: int = 5) -> np.ndarray:
        """
        Predecir secuencia de valores futuros (lógica de negocio)
        
        Args:
            df: DataFrame con características
            target_column: Columna objetivo
            steps: Número de pasos a predecir
            
        Returns:
            Array con predicciones
        """
        try:
            if not self.is_ready or self.lstm_model is None:
                raise ValueError("El modelo debe estar entrenado antes de hacer predicciones")
            
            predictions = []
            current_df = df.copy()
            
            for _ in range(steps):
                # Hacer predicción usando el método del modelo
                pred = self.lstm_model.predict(current_df, target_column)
                predictions.append(pred)
                
                # Actualizar DataFrame para siguiente predicción
                # (En un caso real, necesitarías datos futuros de características)
                # Por ahora, solo agregamos la predicción
                if len(current_df) > 0:
                    # Crear nueva fila con predicción
                    new_row = current_df.iloc[-1].copy()
                    new_row[target_column] = pred
                    # Usar pd.concat correctamente
                    current_df = pd.concat([current_df, pd.DataFrame([new_row])], ignore_index=True)
            
            logger.info(f"Secuencia de {steps} predicciones completada")
            return np.array(predictions)
            
        except Exception as e:
            logger.error(f"Error en predicción de secuencia: {e}")
            raise

    def get_model_status(self) -> Dict[str, Any]:
        """Obtener estado del modelo"""
        return {
            'is_ready': self.is_ready,
            'model_loaded': self.lstm_model is not None,
            'last_prediction': self.last_prediction,
            'prediction_confidence': self.prediction_confidence
        }

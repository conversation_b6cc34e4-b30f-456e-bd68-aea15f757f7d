#!/usr/bin/env python3
"""
Script para probar la nueva configuración del modelo con:
- MSE como función de pérdida
- StandardScaler en lugar de MinMaxScaler
- Dropout reducido (0.05-0.1)
- Target escalado por ATR
"""

import sys
from pathlib import Path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

import pandas as pd
import numpy as np
from datetime import datetime
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_atr_model():
    """
    Probar el modelo con la nueva configuración ATR
    """
    try:
        print("🚀 INICIANDO PRUEBA DEL MODELO CON CONFIGURACIÓN ATR")
        print("⏰ Timestamp:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        print("="*80)
        
        # Importar servicios
        from services.binance_service import BinanceService
        from ai.indicators.technical_indicators import TechnicalIndicators
        from ai.models.lstm_model import LSTMModel
        
        print("📊 1. OBTENIENDO DATOS DE BTC...")
        binance_service = BinanceService()
        
        # Obtener datos históricos (más datos para mejor entrenamiento)
        raw_data = binance_service.get_klines_data('BTCUSDT', '15m', limit=1000)
        
        if raw_data.empty:
            raise ValueError("No se pudieron obtener datos de Binance")
            
        print(f"   ✅ Datos obtenidos: {len(raw_data)} filas")
        print(f"   📈 Rango de precios: ${raw_data['close'].min():.2f} - ${raw_data['close'].max():.2f}")
        
        print("\n🔧 2. CALCULANDO INDICADORES TÉCNICOS (INCLUYENDO ATR)...")
        indicators = TechnicalIndicators()
        df_with_indicators = indicators.calculate_all_indicators(raw_data)
        
        # Verificar que ATR esté presente
        if 'atr' not in df_with_indicators.columns:
            raise ValueError("ATR no fue calculado correctamente")
            
        print(f"   ✅ Indicadores calculados: {len(df_with_indicators.columns)} columnas")
        print(f"   📊 ATR promedio: {df_with_indicators['atr'].mean():.4f}")
        print(f"   📊 ATR rango: [{df_with_indicators['atr'].min():.4f}, {df_with_indicators['atr'].max():.4f}]")
        
        # Limpiar datos
        df_clean = df_with_indicators.dropna()
        print(f"   🧹 Datos limpios: {len(df_clean)} filas")
        
        print("\n🧠 3. CREANDO MODELO LSTM CON NUEVA CONFIGURACIÓN...")
        model = LSTMModel(sequence_length=30, prediction_horizon=1)
        
        print("   📋 Configuración del modelo:")
        print("      - Función de pérdida: MSE")
        print("      - Scaler: StandardScaler")
        print("      - Dropout: 0.05-0.1")
        print("      - Target: ATR-escalado")
        
        print("\n📚 4. PREPARANDO DATOS CON TARGET ATR-ESCALADO...")
        try:
            X, y = model.prepare_data(df_clean, target_column='close')
            print(f"   ✅ Datos preparados: X={X.shape}, y={y.shape}")
            print(f"   📊 Target ATR-escalado - Media: {y.mean():.4f}, Std: {y.std():.4f}")
            print(f"   📊 Target ATR-escalado - Rango: [{y.min():.4f}, {y.max():.4f}]")
        except Exception as e:
            print(f"   ❌ Error preparando datos: {e}")
            return False
        
        print("\n🏋️ 5. ENTRENANDO MODELO (MUESTRA PEQUEÑA)...")
        try:
            # Usar solo una muestra para prueba rápida
            sample_size = min(200, len(df_clean))
            df_sample = df_clean.tail(sample_size)
            
            history = model.train(
                df_sample, 
                target_column='close',
                epochs=5,  # Pocas épocas para prueba rápida
                batch_size=16,
                validation_split=0.2
            )
            
            print(f"   ✅ Modelo entrenado exitosamente")
            print(f"   📊 Loss final: {history['loss'][-1]:.6f}")
            print(f"   📊 Val_loss final: {history['val_loss'][-1]:.6f}")
            
        except Exception as e:
            print(f"   ❌ Error entrenando modelo: {e}")
            return False
        
        print("\n🔮 6. PROBANDO PREDICCIÓN CON DESESCALADO ATR...")
        try:
            # Usar las últimas 30 filas para predicción
            recent_data = df_clean.tail(30)
            prediction = model.predict(recent_data, target_column='close')
            
            current_price = recent_data['close'].iloc[-1]
            current_atr = recent_data['atr'].iloc[-1]
            price_change = prediction - current_price
            atr_change = price_change / current_atr if current_atr > 0 else 0
            
            print(f"   ✅ Predicción realizada exitosamente")
            print(f"   💰 Precio actual: ${current_price:.2f}")
            print(f"   🔮 Precio predicho: ${prediction:.2f}")
            print(f"   📈 Cambio absoluto: ${price_change:.2f}")
            print(f"   📊 Cambio en ATR: {atr_change:.4f}")
            print(f"   📊 ATR actual: {current_atr:.4f}")
            
        except Exception as e:
            print(f"   ❌ Error en predicción: {e}")
            return False
        
        print("\n" + "="*80)
        print("✅ PRUEBA COMPLETADA EXITOSAMENTE")
        print("\n📋 RESUMEN DE CAMBIOS IMPLEMENTADOS:")
        print("   🎯 MSE como función de pérdida (más sensible a errores)")
        print("   📊 StandardScaler (mejor para distribuciones normales)")
        print("   🎛️ Dropout reducido 0.05-0.1 (menos regularización)")
        print("   📈 Target ATR-escalado (reduce heterocedasticidad)")
        print("   🔄 Desescalado ATR en inferencia")
        
        print("\n🎉 VENTAJAS ESPERADAS:")
        print("   • Menor sesgo hacia valores centrales")
        print("   • Mejor manejo de volatilidad variable")
        print("   • Predicciones más sensibles a cambios")
        print("   • Reducción de outliers en el target")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR GENERAL: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_atr_model()
    if success:
        print("\n🎯 La nueva configuración está lista para usar")
    else:
        print("\n⚠️ Se encontraron problemas que necesitan corrección")
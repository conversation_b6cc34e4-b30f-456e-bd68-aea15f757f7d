# 📊 Dashboard BTC - Precios en Tiempo Real

Dashboard interactivo para monitorear precios de Bitcoin (BTC) en tiempo real usando datos de Binance en intervalos de 15 minutos.

## 🚀 Características

- **Precios en Tiempo Real**: Datos actualizados cada 15 minutos desde Binance
- **Gráficas Múltiples**: Alternar entre gráfica de líneas y velas japonesas
- **Velas Japones<PERSON>**: Análisis técnico completo con OHLC (Open, High, Low, Close)
- **Indicadores Técnicos**: SMA, RSI, Bandas de Bollinger y análisis de tendencias
- **Gráfica Interactiva**: Visualización de precios con Plotly
- **Métricas Clave**: Precio actual, volumen 24h, cambio de precio, volatilidad
- **Diseño Responsivo**: Interface moderna con Bootstrap y efectos CSS
- **Actualización Automática**: Datos se refrescan automáticamente
- **Arquitectura Profesional**: Código modular y escalable
- **Sistema de Logging**: Registro completo de eventos y errores

## 📋 Requisitos

- Python 3.8+
- pip o conda

## 🛠️ Instalación

1. **Clonar o descargar el proyecto**
2. **Instalar dependencias**:
   ```bash
   # Opción 1: Instalación como paquete (recomendado)
   pip install -e .
   
   # Opción 2: Instalación directa de dependencias
   pip install dash dash-bootstrap-components pandas requests python-binance plotly python-dotenv
   ```

## 🚀 Uso

1. **Ejecutar la aplicación**:
   ```bash
   python main.py
   ```

2. **Abrir en navegador**:
   ```
   http://localhost:8050
   ```

3. **Probar la estructura**:
   ```bash
   python test_new_structure.py
   ```

## 📊 Funcionalidades

### Dashboard Principal
- **Precio Actual**: Muestra el precio actual de BTC/USDT
- **Volumen 24h**: Volumen total de las últimas 24 horas
- **Última Actualización**: Timestamp de la última actualización

### Gráfica de Precios
- **Tipos**: Líneas y Velas Japonesas (candlestick)
- **Intervalo**: Datos cada 15 minutos
- **Período**: Últimas 24 horas
- **Interactiva**: Zoom, pan, hover con detalles
- **Velas**: Colores verde (subida) y rojo (bajada)

### Tabla de Datos
- **Datos Recientes**: Últimos 5 intervalos de 15 minutos
- **Información**: Hora, precio de cierre, volumen

## 🔧 Configuración

El dashboard utiliza la API pública de Binance, por lo que no requiere API keys para funcionar.

## 🏗️ Arquitectura del Proyecto

```
FinfinLSTM/
├── src/                    # Código fuente principal
│   ├── config/            # Configuración y constantes
│   │   ├── settings.py    # Configuración principal
│   │   └── constants.py   # Constantes del proyecto
│   ├── services/          # Servicios de negocio
│   │   ├── binance_service.py    # Servicio de Binance
│   │   ├── data_service.py       # Procesamiento de datos
│   │   └── chart_service.py      # Generación de gráficas
│   ├── utils/             # Utilidades
│   │   └── logging_setup.py      # Configuración de logging
│   └── app.py             # Aplicación principal
├── logs/                  # Archivos de log
├── main.py                # Punto de entrada
├── setup.py               # Configuración del paquete
├── pyproject.toml         # Configuración moderna del proyecto
└── README.md              # Documentación
```

## 📱 Tecnologías Utilizadas

- **Dash**: Framework web para aplicaciones analíticas
- **Plotly**: Gráficas interactivas y avanzadas
- **Bootstrap**: Diseño responsivo y moderno
- **Python-Binance**: Cliente oficial de Binance
- **Pandas**: Manipulación y análisis de datos
- **Logging**: Sistema de registro profesional

## 🐛 Solución de Problemas

### Error de Conexión
- Verificar conexión a internet
- Comprobar que Binance esté disponible

### Dependencias
- Asegurar que todas las librerías estén instaladas
- Verificar versiones de Python

## 📈 Personalización

Puedes modificar:
- **Símbolo**: Cambiar BTC por otra criptomoneda
- **Intervalo**: Modificar el intervalo de tiempo
- **Período**: Ajustar el rango de datos históricos
- **Estilo**: Personalizar colores y diseño

## 🤝 Contribuciones

Las contribuciones son bienvenidas. Por favor:
1. Fork el proyecto
2. Crea una rama para tu feature
3. Commit tus cambios
4. Push a la rama
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT.

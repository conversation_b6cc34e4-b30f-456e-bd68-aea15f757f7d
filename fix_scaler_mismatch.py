#!/usr/bin/env python3
"""
Script para corregir la discrepancia entre scalers guardados y datos actuales
"""

import pickle
import numpy as np
import pandas as pd
from pathlib import Path
import sys
from sklearn.preprocessing import StandardScaler

# Configurar rutas
project_root = Path(__file__).parent
sys.path.append(str(project_root))
sys.path.append(str(project_root / "src"))

from services.binance_service import BinanceService
from ai.indicators.technical_indicators import TechnicalIndicators
from services.data_processor import DataProcessor

def get_current_data_and_targets():
    """
    Obtener datos actuales y calcular targets ATR-escalados
    """
    print("📊 OBTENIENDO DATOS ACTUALES")
    print("=" * 50)
    
    # Obtener datos históricos extendidos (6 meses)
    binance = BinanceService()
    data = binance.get_historical_data_extended('BTCUSDT', '15m', months=6)
    
    if data is None or len(data) < 1000:
        print(f"❌ Error: Datos insuficientes ({len(data) if data is not None else 0} registros)")
        return None, None, None
    
    print(f"✅ Datos obtenidos: {len(data)} registros")
    
    # Calcular indicadores técnicos
    indicators = TechnicalIndicators()
    data_with_indicators = indicators.calculate_all_indicators(data)
    
    # Limpiar datos
    data_clean = data_with_indicators.dropna()
    
    if len(data_clean) < 100:
        print("❌ Error: Datos insuficientes después de limpieza")
        return None, None, None
    
    print(f"✅ Datos limpios: {len(data_clean)} registros")
    
    # Calcular targets ATR-escalados manualmente
    prediction_horizon = 12
    targets = []
    
    for i in range(len(data_clean) - prediction_horizon):
        current_price = data_clean.iloc[i]['close']
        future_price = data_clean.iloc[i + prediction_horizon]['close']
        atr = data_clean.iloc[i]['atr']
        
        if atr > 0:
            target = (future_price - current_price) / atr
            targets.append(target)
    
    targets = np.array(targets)
    
    # Preparar features (usar las mismas que en el modelo)
    feature_columns = ['open', 'high', 'low', 'volume', 'pivot', 'sma_50', 'rsi', 'stoch_d', 'macd', 'adx', 'macd_histogram']
    
    # Tomar solo los datos que tienen targets correspondientes
    feature_data = data_clean.iloc[:len(targets)][feature_columns].values
    
    print(f"✅ Datos procesados:")
    print(f"   Features: {feature_data.shape}")
    print(f"   Targets: {targets.shape}")
    print(f"   Feature columns: {feature_columns}")
    
    return feature_data, targets, feature_columns

def create_fresh_scalers(X, y, feature_names):
    """
    Crear scalers frescos con los datos actuales
    """
    print("\n🔧 CREANDO SCALERS FRESCOS")
    print("=" * 50)
    
    # X ya está en formato 2D (n_samples, n_features)
    # Crear feature scaler
    feature_scaler = StandardScaler()
    feature_scaler.fit(X)
    feature_scaler.feature_names_in_ = np.array(feature_names)
    
    print(f"📊 Feature Scaler:")
    print(f"   N_features: {feature_scaler.n_features_in_}")
    print(f"   N_samples: {feature_scaler.n_samples_seen_}")
    print(f"   Feature names: {feature_names}")
    
    # Crear target scaler
    target_scaler = StandardScaler()
    y_reshaped = y.reshape(-1, 1)
    target_scaler.fit(y_reshaped)
    
    print(f"\n🎯 Target Scaler:")
    print(f"   Mean: {target_scaler.mean_[0]:.6f}")
    print(f"   Scale: {target_scaler.scale_[0]:.6f}")
    print(f"   Var: {target_scaler.var_[0]:.6f}")
    print(f"   N_samples: {target_scaler.n_samples_seen_}")
    
    # Estadísticas del target
    print(f"\n📈 Estadísticas del target:")
    print(f"   Min: {np.min(y):.6f}")
    print(f"   Max: {np.max(y):.6f}")
    print(f"   Mean: {np.mean(y):.6f}")
    print(f"   Std: {np.std(y):.6f}")
    
    return feature_scaler, target_scaler

def compare_with_old_scalers(new_target_scaler):
    """
    Comparar con scalers antiguos
    """
    print("\n⚖️  COMPARACIÓN CON SCALERS ANTIGUOS")
    print("=" * 50)
    
    scaler_path = project_root / "models" / "btc_lstm_model_scalers.pkl"
    
    if not scaler_path.exists():
        print("❌ No se encontraron scalers antiguos")
        return
    
    with open(scaler_path, 'rb') as f:
        old_scalers = pickle.load(f)
    
    old_target_scaler = old_scalers.get('target_scaler')
    
    if old_target_scaler is None:
        print("❌ No se encontró target_scaler antiguo")
        return
    
    old_mean = old_target_scaler.mean_[0]
    old_scale = old_target_scaler.scale_[0]
    new_mean = new_target_scaler.mean_[0]
    new_scale = new_target_scaler.scale_[0]
    
    mean_diff = abs(old_mean - new_mean)
    scale_diff = abs(old_scale - new_scale)
    
    print(f"📊 Comparación:")
    print(f"   Mean - Antiguo: {old_mean:.6f}, Nuevo: {new_mean:.6f}, Diff: {mean_diff:.6f}")
    print(f"   Scale - Antiguo: {old_scale:.6f}, Nuevo: {new_scale:.6f}, Diff: {scale_diff:.6f}")
    
    # Probar transformaciones
    test_values = np.array([0.0, 0.5, -0.5, 1.0, -1.0]).reshape(-1, 1)
    
    print(f"\n🧪 Impacto en predicciones:")
    print(f"{'Pred Norm':<10} {'Antiguo':<12} {'Nuevo':<12} {'Diferencia':<12}")
    print("-" * 48)
    
    for val in test_values.flatten():
        val_reshaped = np.array([[val]])
        old_result = old_target_scaler.inverse_transform(val_reshaped)[0, 0]
        new_result = new_target_scaler.inverse_transform(val_reshaped)[0, 0]
        diff = abs(old_result - new_result)
        
        print(f"{val:<10.2f} {old_result:<12.6f} {new_result:<12.6f} {diff:<12.6f}")

def save_new_scalers(feature_scaler, target_scaler):
    """
    Guardar los nuevos scalers
    """
    print("\n💾 GUARDANDO SCALERS ACTUALIZADOS")
    print("=" * 50)
    
    # Crear backup del archivo anterior
    scaler_path = project_root / "models" / "btc_lstm_model_scalers.pkl"
    backup_path = project_root / "models" / "btc_lstm_model_scalers_backup.pkl"
    
    if scaler_path.exists():
        import shutil
        shutil.copy2(scaler_path, backup_path)
        print(f"✅ Backup creado: {backup_path}")
    
    # Guardar nuevos scalers
    scalers = {
        'target_scaler': target_scaler,
        'feature_scaler': feature_scaler
    }
    
    with open(scaler_path, 'wb') as f:
        pickle.dump(scalers, f)
    
    print(f"✅ Scalers actualizados guardados: {scaler_path}")
    
    # Verificar guardado
    with open(scaler_path, 'rb') as f:
        loaded_scalers = pickle.load(f)
    
    loaded_target = loaded_scalers['target_scaler']
    print(f"\n🔍 Verificación:")
    print(f"   Target Mean: {loaded_target.mean_[0]:.6f}")
    print(f"   Target Scale: {loaded_target.scale_[0]:.6f}")

def test_prediction_with_new_scalers():
    """
    Probar una predicción con los nuevos scalers
    """
    print("\n🎯 PRUEBA DE PREDICCIÓN CON SCALERS ACTUALIZADOS")
    print("=" * 50)
    
    # Obtener datos actuales para predicción
    binance = BinanceService()
    recent_data = binance.get_klines_data('BTCUSDT', '15m', limit=100)
    
    if recent_data is None or len(recent_data) < 60:
        print("❌ Error obteniendo datos para prueba")
        return
    
    # Calcular indicadores
    indicators = TechnicalIndicators()
    data_with_indicators = indicators.calculate_all_indicators(recent_data)
    
    current_price = data_with_indicators.iloc[-1]['close']
    current_atr = data_with_indicators.iloc[-1]['atr']
    
    print(f"📊 Datos actuales:")
    print(f"   Precio actual: ${current_price:.2f}")
    print(f"   ATR actual: {current_atr:.4f}")
    
    # Simular predicciones normalizadas
    scaler_path = project_root / "models" / "btc_lstm_model_scalers.pkl"
    with open(scaler_path, 'rb') as f:
        scalers = pickle.load(f)
    
    target_scaler = scalers['target_scaler']
    
    test_predictions = [0.0, 0.1, -0.1, 0.5, -0.5]
    
    print(f"\n🧮 Simulación de predicciones:")
    for pred_norm in test_predictions:
        # Desnormalizar
        pred_atr_scaled = target_scaler.inverse_transform([[pred_norm]])[0, 0]
        
        # Convertir a precio
        pred_price = current_price + (pred_atr_scaled * current_atr)
        change_pct = ((pred_price - current_price) / current_price) * 100
        
        print(f"   Pred norm: {pred_norm:6.2f} → ATR-scaled: {pred_atr_scaled:8.4f} → Precio: ${pred_price:8.2f} ({change_pct:+6.2f}%)")

def main():
    print("🔧 CORRECCIÓN DE DISCREPANCIA DE SCALERS")
    print("=" * 60)
    
    # Obtener datos actuales
    X, y, feature_names = get_current_data_and_targets()
    
    if X is None:
        print("❌ Error obteniendo datos. Abortando.")
        return
    
    # Crear scalers frescos
    feature_scaler, target_scaler = create_fresh_scalers(X, y, feature_names)
    
    # Comparar con scalers antiguos
    compare_with_old_scalers(target_scaler)
    
    # Guardar nuevos scalers
    save_new_scalers(feature_scaler, target_scaler)
    
    # Probar predicción
    test_prediction_with_new_scalers()
    
    print("\n✅ Corrección de scalers completada")
    print("\n⚠️  IMPORTANTE: Ahora debes re-entrenar el modelo con estos scalers actualizados")
    print("   Ejecuta: python train_and_save_model.py")

if __name__ == "__main__":
    main()
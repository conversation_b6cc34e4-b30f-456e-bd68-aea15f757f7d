# 🚀 Dashboard BTC - Precios en Tiempo Real

Dashboard interactivo para monitorear precios de Bitcoin (BTC) en tiempo real usando datos de Binance en intervalos de 15 minutos.

## 📁 **Estructura del Proyecto**

```
FinfinLSTM/
├── 📁 src/                    # Código fuente principal
│   ├── 📁 config/            # Configuración y constantes
│   ├── 📁 services/          # Servicios de negocio
│   ├── 📁 utils/             # Utilidades
│   └── app.py                # Aplicación principal
├── 📁 docs/                  # Documentación del proyecto
│   ├── README.md             # Documentación completa
│   └── CLEANUP_SUMMARY.md    # Resumen de limpieza
├── 📁 tests/                 # Pruebas y validaciones
│   └── test_new_structure.py # Pruebas de la estructura
├── 📁 scripts/               # Scripts de configuración
│   ├── setup.py              # Configuración del paquete
│   ├── pyproject.toml        # Configuración moderna
│   └── env.example           # Variables de entorno
├── 📁 logs/                  # Archivos de log
├── main.py                   # Punto de entrada principal
└── .gitignore                # Archivos ignorados por Git
```

## 🚀 **Inicio Rápido**

### **1. Instalación**
```bash
# Opción 1: Instalación como paquete (recomendado)
pip install -e scripts/

# Opción 2: Instalación directa
pip install dash dash-bootstrap-components pandas requests python-binance plotly python-dotenv
```

### **2. Ejecución**
```bash
python main.py
```

### **3. Acceso**
Abre tu navegador en: `http://localhost:8050`

## 📚 **Documentación**

- **📖 [Documentación Completa](docs/README.md)** - Guía completa del proyecto
- **🧹 [Resumen de Limpieza](docs/CLEANUP_SUMMARY.md)** - Detalles de la optimización del código

## 🧪 **Pruebas**

```bash
python tests/test_new_structure.py
```

## ⚙️ **Configuración**

- **Variables de entorno**: Ver `scripts/env.example`
- **Configuración del paquete**: Ver `scripts/setup.py` y `scripts/pyproject.toml`

## 🎯 **Características Principales**

- ✅ **Precios en Tiempo Real** - Datos cada 15 minutos desde Binance
- ✅ **Gráficas Múltiples** - Líneas y velas japonesas
- ✅ **Arquitectura Modular** - Código organizado y escalable
- ✅ **Sistema de Logging** - Registro completo de eventos
- ✅ **Diseño Responsivo** - Interface moderna con Bootstrap

## 🔧 **Tecnologías**

- **Dash** - Framework web para aplicaciones analíticas
- **Plotly** - Gráficas interactivas y avanzadas
- **Python-Binance** - Cliente oficial de Binance
- **Pandas** - Manipulación y análisis de datos

---

**📖 Para más detalles, consulta la [documentación completa](docs/README.md)**

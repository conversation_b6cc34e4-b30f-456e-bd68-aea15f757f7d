[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "dashboard-btc"
version = "1.0.0"
description = "Dashboard profesional para monitoreo de precios de Bitcoin en tiempo real"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Tu Nombre", email = "<EMAIL>"}
]
keywords = ["bitcoin", "dashboard", "trading", "binance", "cryptocurrency"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Office/Business :: Financial :: Investment",
    "Topic :: Scientific/Engineering :: Information Analysis",
]
requires-python = ">=3.8"
dependencies = [
    "dash>=2.14.2",
    "dash-bootstrap-components>=1.5.0",
    "pandas>=2.1.4",
    "requests>=2.31.0",
    "python-binance>=1.0.19",
    "plotly>=5.17.0",
    "python-dotenv>=1.0.0",
    "numpy>=1.22.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-cov>=2.0",
    "black>=21.0",
    "flake8>=3.8",
    "mypy>=0.800",
    "pre-commit>=2.0",
]

[project.scripts]
dashboard-btc = "main:main"

[project.urls]
Homepage = "https://github.com/tuusuario/dashboard-btc"
Repository = "https://github.com/tuusuario/dashboard-btc"
Documentation = "https://github.com/tuusuario/dashboard-btc#readme"
Issues = "https://github.com/tuusuario/dashboard-btc/issues"

[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

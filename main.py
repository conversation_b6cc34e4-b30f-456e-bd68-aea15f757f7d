#!/usr/bin/env python3
"""
Punto de entrada principal para el Dashboard BTC
"""
import sys
import os
from pathlib import Path

# Agregar el directorio src al path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def main():
    """Función principal"""
    try:
        from src.app import main as dashboard_main
        dashboard_main()
    except ImportError as e:
        print(f"Error importando módulos: {e}")
        print("Asegúrate de que todas las dependencias estén instaladas:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"Error ejecutando dashboard: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

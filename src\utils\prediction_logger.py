"""Logger especializado para predicciones
Separa el logging detallado del modelo LSTM
"""
import numpy as np
import logging
from typing import Any, Optional

class PredictionLogger:
    """Logger especializado para monitorear predicciones"""
    
    def __init__(self, logger_name: str = 'prediction'):
        """Inicializar logger de predicciones
        
        Args:
            logger_name: Nombre del logger
        """
        self.logger = logging.getLogger(logger_name)
        self.logger.setLevel(logging.INFO)
    
    def log_input_data(self, current_price: float, data_length: int) -> None:
        """Registrar información de datos de entrada
        
        Args:
            current_price: Precio actual
            data_length: Cantidad de datos disponibles
        """
        try:
            self.logger.info(
                f"INICIO_PREDICCION|precio_actual={current_price:.2f}|"
                f"datos_disponibles={data_length}"
            )
        except Exception as e:
            self.logger.error(f"Error logging entrada: {e}")
    
    def log_scaled_prediction(self, prediction_scaled: np.ndarray) -> None:
        """Registrar predicción escalada
        
        Args:
            prediction_scaled: Predicción en escala normalizada
        """
        try:
            if len(prediction_scaled.shape) > 1 and prediction_scaled.shape[0] > 0:
                scaled_value = float(prediction_scaled[0][0])
            else:
                scaled_value = float(prediction_scaled[0])
                
            self.logger.info(
                f"PREDICCION_ESCALADA|valor={scaled_value:.6f}|shape={prediction_scaled.shape}"
            )
            
            # Verificar predicción escalada
            if np.isnan(prediction_scaled).any():
                self.logger.error(f"PREDICCION_NAN|modelo_genero_nan=True")
                raise ValueError("El modelo generó predicciones NaN")
                
        except Exception as e:
            self.logger.error(f"Error logging predicción escalada: {e}")
            raise
    
    def log_final_prediction(self, prediction: float) -> None:
        """Registrar predicción final
        
        Args:
            prediction: Predicción final
        """
        try:
            self.logger.info(f"PREDICCION_DESNORMALIZADA|valor={prediction:.2f}")
            
            # Verificar predicción final
            if np.isnan(prediction):
                self.logger.error(f"DESNORMALIZACION_NAN|scaler_problema=True")
                raise ValueError("Error en desnormalización: predicción final contiene NaN")
            
        except Exception as e:
            self.logger.error(f"Error logging predicción final: {e}")
            raise
    
    def log_prediction_result(self, current_price: float, predicted_price: float, 
                            change_pct: float, confidence: float) -> None:
        """Registrar resultado de predicción
        
        Args:
            current_price: Precio actual
            predicted_price: Precio predicho
            change_pct: Cambio porcentual
            confidence: Nivel de confianza
        """
        try:
            self.logger.info(
                f"CALCULO_CAMBIO|precio_actual={current_price:.2f}|"
                f"precio_predicho={predicted_price:.2f}|"
                f"diferencia={predicted_price-current_price:.2f}|"
                f"cambio_pct={change_pct:.2f}%"
            )
            
            self.logger.info(
                f"RESULTADO_PREDICCION|precio_predicho={predicted_price:.2f}|"
                f"cambio_pct={change_pct:+.2f}|confianza={confidence:.3f}"
            )
            
        except Exception as e:
            self.logger.error(f"Error logging resultado: {e}")
    
    def log_high_volatility_warning(self, change_pct: float, current_price: float, 
                                  predicted_price: float) -> None:
        """Registrar advertencia de alta volatilidad
        
        Args:
            change_pct: Cambio porcentual
            current_price: Precio actual
            predicted_price: Precio predicho
        """
        try:
            self.logger.warning(
                f"PREDICCION_ALTA_VOLATILIDAD|cambio_pct={change_pct:.2f}%|"
                f"precio_actual={current_price:.2f}|"
                f"precio_predicho={predicted_price:.2f}|"
                f"diferencia_absoluta={abs(predicted_price-current_price):.2f}"
            )
            
            if abs(change_pct) > 50:
                self.logger.error(
                    f"PREDICCION_EXTREMA_POSIBLE_ERROR_ESCALADO|"
                    f"cambio_pct={change_pct:.2f}%|revisar_scaler=True"
                )
        except Exception as e:
            self.logger.error(f"Error logging volatilidad: {e}")
    
    def log_low_confidence_warning(self, confidence: float, volatility: float) -> None:
        """Registrar advertencia de baja confianza
        
        Args:
            confidence: Nivel de confianza
            volatility: Volatilidad
        """
        try:
            self.logger.warning(
                f"CONFIANZA_BAJA|confianza={confidence:.3f}|"
                f"volatilidad_alta={volatility:.2f}"
            )
        except Exception as e:
            self.logger.error(f"Error logging confianza: {e}")
    
    def log_prediction_error(self, error: Exception) -> None:
        """Registrar errores en predicción
        
        Args:
            error: Excepción ocurrida
        """
        self.logger.error(f"ERROR_PREDICCION_PRECIO|tipo={type(error).__name__}|mensaje={str(error)}")
    
    def log_sequence_prediction(self, step: int, prediction: float, total_steps: int) -> None:
        """Registrar predicción de secuencia
        
        Args:
            step: Paso actual
            prediction: Predicción del paso
            total_steps: Total de pasos
        """
        self.logger.info(
            f"SECUENCIA_PREDICCION|paso={step}/{total_steps}|"
            f"prediccion={prediction:.2f}"
        )
    
    def set_log_level(self, level: int) -> None:
        """Cambiar nivel de logging
        
        Args:
            level: Nivel de logging (logging.DEBUG, INFO, WARNING, etc.)
        """
        self.logger.setLevel(level)
    
    def enable_detailed_logging(self) -> None:
        """Habilitar logging detallado (DEBUG)"""
        self.set_log_level(logging.DEBUG)
    
    def disable_detailed_logging(self) -> None:
        """Deshabilitar logging detallado (INFO)"""
        self.set_log_level(logging.INFO)
"""
Configuración de logging para el dashboard
"""
import logging
import logging.handlers
from pathlib import Path
from typing import Dict

def setup_logging(config: Dict):
    """
    Configurar sistema de logging
    
    Args:
        config: Configuración de logging
    """
    try:
        # Crear directorio de logs si no existe
        log_file = Path(config['file'])
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Configurar formato
        formatter = logging.Formatter(config['format'])
        
        # Configurar nivel de logging
        level = getattr(logging, config['level'].upper())
        
        # Configurar logger raíz
        root_logger = logging.getLogger()
        root_logger.setLevel(level)
        
        # Handler para consola
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
        # Handler para archivo con manejo seguro de permisos en Windows
        try:
            # Usar TimedRotatingFileHandler que es más seguro en Windows
            file_handler = logging.handlers.TimedRotatingFileHandler(
                log_file,
                when='midnight',
                interval=1,
                backupCount=7,
                delay=True
            )
            file_handler.setLevel(level)
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
        except PermissionError:
            # Fallback: usar archivo simple con timestamp único
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            fallback_file = log_file.parent / f"dashboard_{timestamp}.log"
            file_handler = logging.FileHandler(fallback_file)
            file_handler.setLevel(level)
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
            print(f"Usando archivo de log alternativo: {fallback_file}")
        
        # Configurar loggers específicos
        logging.getLogger('dash').setLevel(logging.WARNING)
        logging.getLogger('werkzeug').setLevel(logging.WARNING)
        
        logging.info("Sistema de logging configurado correctamente")
        
    except Exception as e:
        print(f"Error configurando logging: {e}")
        # Configuración básica de fallback
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )


# Agregar después de la línea 50
def setup_prediction_logger():
    """Configurar logger específico para predicciones con formato estructurado"""
    prediction_logger = logging.getLogger('prediction')
    prediction_logger.setLevel(logging.DEBUG)
    
    # Handler específico para predicciones con manejo seguro en Windows
    try:
        prediction_handler = logging.handlers.TimedRotatingFileHandler(
            'logs/predictions.log',
            when='midnight',
            interval=1,
            backupCount=10,
            delay=True
        )
    except PermissionError:
        # Fallback: usar archivo con timestamp único
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        prediction_handler = logging.FileHandler(f'logs/predictions_{timestamp}.log')
    
    # Formato estructurado para análisis
    prediction_formatter = logging.Formatter(
        '%(asctime)s|%(levelname)s|%(funcName)s|%(message)s'
    )
    prediction_handler.setFormatter(prediction_formatter)
    prediction_logger.addHandler(prediction_handler)
    
    return prediction_logger

#!/usr/bin/env python3
"""
Prueba simple de la nueva estructura del proyecto
"""
import sys
from pathlib import Path

def main():
    """Función principal de prueba"""
    print("Probando nueva estructura del proyecto...")
    
    # Agregar src al path
    src_path = Path(__file__).parent.parent / "src"
    sys.path.insert(0, str(src_path))
    
    try:
        # Probar configuración
        print("1. Probando configuración...")
        from config.settings import BINANCE_CONFIG, SERVER_CONFIG
        print(f"   ✅ Configuración cargada - Puerto: {SERVER_CONFIG['port']}")
        
        # Probar servicios
        print("2. Probando servicios...")
        from services.binance_service import BinanceService
        from services.data_service import DataService
        from services.chart_service import ChartService
        print("   ✅ Servicios importados correctamente")
        
        # Probar aplicación
        print("3. Probando aplicación...")
        from app import DashboardApp
        print("   ✅ Aplicación importada correctamente")
        
        # Probar utilidades
        print("4. Probando utilidades...")
        from utils.logging_setup import setup_logging
        print("   ✅ Utilidades importadas correctamente")
        
        print("\n🎉 ¡Todas las pruebas pasaron exitosamente!")
        return True
        
    except ImportError as e:
        print(f"❌ Error de importación: {e}")
        return False
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

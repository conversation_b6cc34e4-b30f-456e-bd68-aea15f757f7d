#!/usr/bin/env python3
"""
Diagnóstico detallado del flujo de datos en la predicción
"""

import sys
import os
import numpy as np
import pandas as pd
from pathlib import Path
import logging

# Configurar logging detallado
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s - %(name)s - %(message)s')

# Agregar src al path
src_path = Path(__file__).parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

# Importaciones
try:
    from src.ai.predictors.price_predictor import PricePredictor
    from src.services.binance_service import BinanceService
    from src.ai.indicators.technical_indicators import TechnicalIndicators
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent))
    from src.ai.predictors.price_predictor import PricePredictor
    from src.services.binance_service import BinanceService
    from src.ai.indicators.technical_indicators import TechnicalIndicators

def main():
    print("🔍 DIAGNÓSTICO COMPLETO DEL FLUJO DE DATOS")
    print("=" * 60)
    
    # 1. Obtener datos originales
    print("\n1. 📊 OBTENIENDO DATOS ORIGINALES...")
    binance = BinanceService()
    raw_data = binance.get_klines_data('BTCUSDT', '1h', 100)
    
    print(f"   ✅ Datos obtenidos: {len(raw_data)} filas")
    print(f"   📊 Columnas: {list(raw_data.columns)}")
    print(f"   💰 Precio actual: ${raw_data['close'].iloc[-1]:,.2f}")
    print(f"   📈 Rango precios: ${raw_data['close'].min():,.2f} - ${raw_data['close'].max():,.2f}")
    
    # 2. Crear predictor y cargar modelo
    print("\n2. 🤖 CARGANDO MODELO...")
    predictor = PricePredictor()
    model_path = "models/btc_lstm_model.h5"
    
    if os.path.exists(model_path):
        predictor.load_model(model_path)
        print("   ✅ Modelo cargado exitosamente")
        print(f"   🎯 Estado del modelo: {predictor.get_model_status()}")
    else:
        print("   ❌ Modelo no encontrado")
        return
    
    # 3. Preparar datos para predicción (paso a paso)
    print("\n3. 🔧 PREPARANDO DATOS PASO A PASO...")
    
    # 3.1 Calcular indicadores técnicos
    print("\n   3.1 📈 Calculando indicadores técnicos...")
    tech_indicators = TechnicalIndicators()
    data_with_indicators = tech_indicators.calculate_all_indicators(raw_data)
    
    print(f"       ✅ Indicadores calculados")
    print(f"       📊 Columnas después de indicadores: {len(data_with_indicators.columns)}")
    print(f"       📋 Nuevas columnas: {sorted(data_with_indicators.columns.tolist())}")
    
    # 3.2 Obtener columnas objetivo
    print("\n   3.2 🎯 Obteniendo columnas objetivo...")
    target_columns = tech_indicators.get_feature_columns()
    print(f"       📋 Columnas objetivo ({len(target_columns)}): {target_columns}")
    
    # 3.3 Verificar compatibilidad
    print("\n   3.3 🔍 Verificando compatibilidad...")
    available_cols = set(data_with_indicators.columns)
    target_cols = set(target_columns)
    missing_cols = target_cols - available_cols
    extra_cols = available_cols - target_cols
    
    print(f"       ✅ Columnas disponibles: {len(available_cols)}")
    print(f"       🎯 Columnas requeridas: {len(target_cols)}")
    if missing_cols:
        print(f"       ❌ Columnas faltantes: {sorted(missing_cols)}")
    if extra_cols:
        print(f"       ➕ Columnas extra: {sorted(extra_cols)}")
    
    # 3.4 Seleccionar características finales
    print("\n   3.4 ✂️  Seleccionando características finales...")
    try:
        feature_data = data_with_indicators[target_columns].copy()
        print(f"       ✅ Características seleccionadas: {feature_data.shape}")
        print(f"       📊 Rango de valores:")
        for col in feature_data.columns[:5]:  # Mostrar solo las primeras 5
            print(f"           {col}: {feature_data[col].min():.4f} - {feature_data[col].max():.4f}")
        if len(feature_data.columns) > 5:
            print(f"           ... y {len(feature_data.columns) - 5} columnas más")
    except KeyError as e:
        print(f"       ❌ Error seleccionando características: {e}")
        return
    
    # 4. Verificar scaler
    print("\n4. ⚖️  VERIFICANDO SCALER...")
    if predictor.feature_scaler:
        scaler = predictor.feature_scaler
        expected_features = scaler.n_features_in_
        actual_features = len(feature_data.columns)
        
        print(f"   🔍 Tipo de scaler: {type(scaler).__name__}")
        print(f"   📊 Features esperadas: {expected_features}")
        print(f"   📊 Features actuales: {actual_features}")
        
        if hasattr(scaler, 'center_'):
            print(f"   📊 Center (mediana): {scaler.center_[:5] if len(scaler.center_) > 5 else scaler.center_}")
        if hasattr(scaler, 'scale_'):
            print(f"   📊 Scale (IQR): {scaler.scale_[:5] if len(scaler.scale_) > 5 else scaler.scale_}")
        
        if actual_features != expected_features:
            print(f"   ⚠️  DESAJUSTE DE CARACTERÍSTICAS: {actual_features} vs {expected_features}")
        else:
            print(f"   ✅ CARACTERÍSTICAS PERFECTAMENTE ALINEADAS")
    else:
        print("   ❌ No hay feature_scaler cargado")
    
    # 5. Verificar target scaler
    print("\n5. 🎯 VERIFICANDO TARGET SCALER...")
    if predictor.target_scaler:
        target_scaler = predictor.target_scaler
        print(f"   🔍 Tipo: {type(target_scaler).__name__}")
        
        if hasattr(target_scaler, 'data_min_'):
            print(f"   📊 Data min: {target_scaler.data_min_}")
        if hasattr(target_scaler, 'data_max_'):
            print(f"   📊 Data max: {target_scaler.data_max_}")
        if hasattr(target_scaler, 'scale_'):
            print(f"   📊 Scale: {target_scaler.scale_}")
        
        # Probar normalización del precio actual
        current_price = raw_data['close'].iloc[-1]
        normalized = target_scaler.transform([[current_price]])[0][0]
        denormalized = target_scaler.inverse_transform([[normalized]])[0][0]
        
        print(f"   🧪 PRUEBA DE NORMALIZACIÓN:")
        print(f"       Precio actual: ${current_price:,.2f}")
        print(f"       Normalizado: {normalized:.6f}")
        print(f"       Desnormalizado: ${denormalized:,.2f}")
        print(f"       Error: ${abs(current_price - denormalized):,.2f}")
    else:
        print("   ❌ No hay target_scaler cargado")
    
    # 6. Hacer predicción completa con diagnóstico
    print("\n6. 🔮 HACIENDO PREDICCIÓN COMPLETA...")
    try:
        predicted_price, confidence = predictor.predict_next_price(raw_data)
        current_price = raw_data['close'].iloc[-1]
        change_pct = ((predicted_price - current_price) / current_price) * 100
        
        print(f"   ✅ PREDICCIÓN COMPLETADA:")
        print(f"       💰 Precio actual: ${current_price:,.2f}")
        print(f"       🔮 Precio predicho: ${predicted_price:,.2f}")
        print(f"       📊 Cambio: {change_pct:+.2f}%")
        print(f"       🎯 Confianza: {confidence:.2%}")
        print(f"       📏 Diferencia absoluta: ${abs(predicted_price - current_price):,.2f}")
        
        # Análisis de la predicción
        if abs(change_pct) > 10:
            print(f"       ⚠️  PREDICCIÓN EXTREMA: Cambio > 10%")
        elif abs(change_pct) > 5:
            print(f"       ⚠️  PREDICCIÓN ALTA: Cambio > 5%")
        else:
            print(f"       ✅ PREDICCIÓN RAZONABLE: Cambio < 5%")
            
    except Exception as e:
        print(f"   ❌ Error en predicción: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🔍 DIAGNÓSTICO COMPLETADO")
    
    # 7. Resumen de hallazgos
    print("\n7. 📋 RESUMEN DE HALLAZGOS:")
    print("   ✅ Datos se obtienen correctamente de Binance")
    print("   ✅ Indicadores técnicos se calculan correctamente")
    print("   ✅ Modelo se carga correctamente")
    print("   ✅ Scalers se cargan correctamente")
    
    if 'predicted_price' in locals():
        if abs(change_pct) > 10:
            print("   ❌ PROBLEMA: Predicción muy alejada del precio actual")
            print("   💡 CAUSA PROBABLE: Modelo entrenado con datos antiguos")
            print("   🔧 SOLUCIÓN: Reentrenar modelo con datos más recientes")
        else:
            print("   ✅ Predicción dentro de rango razonable")

if __name__ == "__main__":
    main()
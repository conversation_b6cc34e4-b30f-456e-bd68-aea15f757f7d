#!/usr/bin/env python3
"""
Sistema de Adaptación Dinámica para Modelo LSTM
Detecta automáticamente cuando el modelo necesita reentrenamiento
"""
import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdaptiveLSTMSystem:
    """
    Sistema simplificado - PREDICCIÓN DIRECTA con modelo LSTM
    Sin detección de régimen de mercado, va directo a predicciones
    """

    def __init__(self, model_path=None):
        # Ruta absoluta al modelo desde cualquier directorio
        if model_path is None:
            # Obtener la ruta absoluta al directorio padre
            src_dir = Path(__file__).parent.parent.parent
            model_path = str(src_dir / "models" / "btc_lstm_model.h5")

        self.model_path = model_path
        
        # Importar servicios
        try:
            # Agregar src al path para importaciones
            src_path = Path(__file__).parent.parent.parent
            if str(src_path) not in sys.path:
                sys.path.insert(0, str(src_path))

            from services.binance_service import BinanceService
            from ai.predictors.price_predictor import PricePredictor
            self.binance_service = BinanceService()
            self.predictor = None
            self.load_model()
        except ImportError as e:
            logger.error(f"Error importando servicios: {e}")
            self.binance_service = None
            self.predictor = None
    
    def load_model(self):
        """Cargar modelo LSTM ya entrenado"""
        try:
            logger.info(f"Intentando cargar modelo desde: {self.model_path}")
            logger.info(f"Archivo existe: {os.path.exists(self.model_path)}")

            # Verificar archivos relacionados
            scalers_path = self.model_path.replace('.h5', '_scalers.pkl')
            logger.info(f"Archivo de scalers: {scalers_path}")
            logger.info(f"Scalers existe: {os.path.exists(scalers_path)}")

            if os.path.exists(self.model_path) and os.path.exists(scalers_path):
                logger.info("Importando PricePredictor...")
                from ai.predictors.price_predictor import PricePredictor

                logger.info("Creando instancia de PricePredictor...")
                self.predictor = PricePredictor(model_path=self.model_path)

                # Verificar que se cargó correctamente
                if self.predictor and self.predictor.is_ready:
                    logger.info("Modelo LSTM cargado exitosamente!")
                    logger.info(f"   Ruta: {self.model_path}")
                    logger.info(f"   Estado: {self.predictor.get_model_status()}")
                    return True
                else:
                    logger.error("Modelo se creó pero no está listo")
                    if self.predictor:
                        status = self.predictor.get_model_status()
                        logger.info(f"   Estado del predictor: {status}")
                    else:
                        logger.info("   Predictor es None")
                    return False
            else:
                logger.error(f"Archivo del modelo no encontrado: {self.model_path}")
                logger.error(f"Archivo de scalers no encontrado: {scalers_path}")
                logger.error("Ejecuta primero: python train_and_save_model.py")
                return False
        except ImportError as e:
            logger.error(f"Error de importación: {e}")
            logger.error("Verifica que todos los módulos estén instalados")
            return False
        except Exception as e:
            logger.error(f"Error cargando modelo: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    def detect_market_regime(self, current_data):
        """
        VERSIÓN SIMPLIFICADA: Siempre permite predicciones LSTM
        Returns: (regime, out_of_range_pct, details)
        """
        try:
            logger.info("Sistema simplificado - Siempre permite predicciones LSTM")
            return "ACTIVE", 0.0, {"regime": "ACTIVE", "message": "Sistema activo para predicciones"}

        except Exception as e:
            logger.error(f"Error en sistema simplificado: {e}")
            return "ACTIVE", 0.0, {"regime": "ACTIVE"}
    
    def should_retrain(self, regime, out_of_range_pct, consecutive_count=0):
        """
        VERSIÓN SIMPLIFICADA: Nunca reentrena automáticamente
        """
        return False, "Sistema simplificado - No reentrena automáticamente"
    
    def get_adaptive_recommendation(self, regime, out_of_range_pct):
        """
        VERSIÓN SIMPLIFICADA: Recomendación básica siempre
        """
        return {
            "action": "TRADE_ACTIVE",
            "confidence": "ALTA",
            "message": "Sistema activo - Predicción LSTM disponible",
            "risk_level": "NORMAL",
            "position_size": "100%",
            "stop_loss": "Estándar"
        }
    
    def adaptive_prediction(self, data):
        """
        VERSIÓN SIMPLIFICADA: Predicción directa con modelo LSTM
        Sin lógica de régimen de mercado, va directo a predicciones
        """
        try:
            logger.info(f"INICIO_PREDICCION_ADAPTATIVA|datos_disponibles={len(data) if hasattr(data, '__len__') else 'N/A'}|columnas={list(data.columns) if hasattr(data, 'columns') else 'N/A'}")

            # Hacer predicción directa si el modelo está cargado
            if self.predictor:
                logger.info(f"ESTADO_PREDICTOR|existe={self.predictor is not None}|listo={self.predictor.is_ready}")

                try:
                    # Log de datos de entrada
                    if hasattr(data, 'shape') and hasattr(data, 'columns'):
                        current_price = data['close'].iloc[-1] if 'close' in data.columns else None
                        precio_actual_str = f"{current_price:.2f}" if current_price else 'N/A'
                        min_precio_str = f"{data['close'].min():.2f}" if 'close' in data.columns else 'N/A'
                        max_precio_str = f"{data['close'].max():.2f}" if 'close' in data.columns else 'N/A'
                        logger.info(f"DATOS_ENTRADA|shape={data.shape}|precio_actual={precio_actual_str}|min_precio={min_precio_str}|max_precio={max_precio_str}")
                    
                    logger.info("EJECUTANDO_PREDICCION_LSTM|metodo=predict_next_price|columna_objetivo=close")
                    predicted_price, confidence = self.predictor.predict_next_price(data, 'close')
                    
                    # Log de resultado de predicción
                    logger.info(f"RESULTADO_PREDICCION_LSTM|precio_predicho={predicted_price:.2f}|confianza={confidence:.3f}")
                    
                    # Calcular cambio porcentual si es posible
                    change_percent = None
                    if hasattr(data, 'columns') and 'close' in data.columns:
                        current_price = data['close'].iloc[-1]
                        change_percent = ((predicted_price - current_price) / current_price) * 100
                        logger.info(f"CALCULO_CAMBIO|precio_actual={current_price:.2f}|precio_predicho={predicted_price:.2f}|cambio_pct={change_percent:.2f}%")
                        
                        # Verificar si el cambio es inusual
                        if abs(change_percent) > 5:  # Cambios mayores al 5%
                            logger.warning(f"CAMBIO_SIGNIFICATIVO_DETECTADO|cambio_pct={change_percent:.2f}%|revisar_contexto=True")

                    prediction_result = {
                        "price": predicted_price,
                        "confidence": confidence,
                        "regime": "ACTIVE",
                        "change_percent": change_percent,
                        "recommendation": {
                            "action": "PREDICT",
                            "confidence": confidence,
                            "position_size": "FULL",
                            "risk_level": "NORMAL"
                        }
                    }

                    cambio_str = f"{change_percent:.2f}" if change_percent is not None else "N/A"
                    confianza_pct = confidence * 100
                    logger.info(f"PREDICCION_ADAPTATIVA_COMPLETADA|precio=${predicted_price:,.2f}|confianza={confianza_pct:.1f}%|cambio={cambio_str}%")

                    return prediction_result

                except Exception as e:
                    import traceback
                    logger.error(f"ERROR_PREDICCION_LSTM|tipo={type(e).__name__}|mensaje={str(e)}")
                    logger.error(f"TRACEBACK_COMPLETO: {traceback.format_exc()}")
                    return {
                        "price": None,
                        "confidence": 0.0,
                        "regime": "ERROR",
                        "recommendation": {"action": "ERROR"},
                        "error": str(e)
                    }
            else:
                logger.error("ERROR_MODELO_NO_CARGADO|predictor=None|verificar_carga_modelo=True")
                return {
                    "price": None,
                    "confidence": 0.0,
                    "regime": "NO_MODEL",
                    "recommendation": {"action": "NO_MODEL"}
                }

        except Exception as e:
            logger.error(f"ERROR_SISTEMA_ADAPTATIVO|tipo={type(e).__name__}|mensaje={str(e)}")
            return None
    
    def auto_retrain_if_needed(self, data):
        """
        Reentrenamiento automático si es necesario
        """
        try:
            regime, out_of_range_pct, _ = self.detect_market_regime(data)
            needs_retrain, reason = self.should_retrain(regime, out_of_range_pct, self.consecutive_out_of_range)
            
            if needs_retrain:
                logger.warning(f"REENTRENAMIENTO AUTOMATICO NECESARIO:")
                logger.warning(f"   Regimen: {regime}")
                logger.warning(f"   Fuera del rango: {out_of_range_pct:.1f}%")
                logger.warning(f"   Razon: {reason}")
                
                # Ejecutar reentrenamiento
                success = self.execute_auto_retrain(data)
                
                if success:
                    logger.info("Reentrenamiento automatico completado")
                    self.consecutive_out_of_range = 0
                    self.last_retrain_date = datetime.now()
                    self.retrain_count += 1
                    return True
                else:
                    logger.error("Error en reentrenamiento automatico")
                    return False
            else:
                logger.info("No se necesita reentrenamiento automatico")
                return False
                
        except Exception as e:
            logger.error(f"Error en reentrenamiento automático: {e}")
            return False
    
    def execute_auto_retrain(self, data):
        """
        Ejecutar reentrenamiento automático
        """
        try:
            logger.info("Iniciando reentrenamiento automático...")
            
            # Obtener más datos para reentrenamiento
            logger.info("Obteniendo datos adicionales para reentrenamiento...")
            additional_data = self.binance_service.get_klines_data('BTCUSDT', '15m', 2000)

            if additional_data is None or len(additional_data) < 1000:
                logger.error("No se pudieron obtener datos suficientes para reentrenamiento")
                return False

            # Combinar datos actuales con históricos
            combined_data = pd.concat([data, additional_data]).drop_duplicates().reset_index(drop=True)
            logger.info(f"Datos combinados: {len(combined_data)} registros")
            
            # Reentrenar modelo
            logger.info("Reentrenando modelo LSTM...")
            history = self.predictor.train_model(
                combined_data,
                target_column='close',
                epochs=15,  # Más épocas para mejor adaptación
                save_model=True
            )
            
            # Actualizar rangos entrenados
            new_min = combined_data['close'].min()
            new_max = combined_data['close'].max()
            self.trained_min_price = new_min
            self.trained_max_price = new_max
            self.trained_range = new_max - new_min
            
            logger.info(f"Modelo reentrenado exitosamente")
            logger.info(f"Nuevo rango entrenado: ${new_min:,.2f} - ${new_max:,.2f}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error ejecutando reentrenamiento automático: {e}")
            return False
    
    def get_system_status(self):
        """
        Obtener estado del sistema adaptativo
        """
        return {
            "trained_min_price": self.trained_min_price,
            "trained_max_price": self.trained_max_price,
            "trained_range": self.trained_range,
            "consecutive_out_of_range": self.consecutive_out_of_range,
            "last_retrain_date": self.last_retrain_date,
            "retrain_count": self.retrain_count,
            "model_loaded": self.predictor is not None,
            "binance_service": self.binance_service is not None
        }
    
    def reset_counters(self):
        """Resetear contadores de adaptación"""
        self.consecutive_out_of_range = 0
        self.prediction_errors = []
        logger.info("Contadores de adaptación reseteados")
    
    def evaluate_prediction_quality(self, actual_price, predicted_price):
        """
        Evaluar la calidad de una predicción y decidir si reentrenar
        """
        try:
            if predicted_price is None or actual_price is None:
                return False, "Predicción o precio real no disponible"
            
            # Calcular error porcentual
            error_pct = abs(predicted_price - actual_price) / actual_price * 100
            
            # Agregar error al historial
            self.prediction_errors.append(error_pct)
            
            # Mantener solo los últimos 20 errores
            if len(self.prediction_errors) > 20:
                self.prediction_errors = self.prediction_errors[-20:]
            
            # Calcular precisión promedio
            if len(self.prediction_errors) >= 5:
                avg_error = sum(self.prediction_errors) / len(self.prediction_errors)
                accuracy = max(0, 100 - avg_error)
                
                logger.info(f"EVALUACIÓN DE CALIDAD:")
                logger.info(f"   Error actual: {error_pct:.2f}%")
                logger.info(f"   Error promedio: {avg_error:.2f}%")
                logger.info(f"   Precisión: {accuracy:.1f}%")
                
                # Reentrenar si la precisión es baja
                if accuracy < self.accuracy_threshold * 100:
                    return True, f"PRECISIÓN BAJA: {accuracy:.1f}% < {self.accuracy_threshold * 100}%"
                
                # Reentrenar si hay muchos errores consecutivos
                if len(self.prediction_errors) >= self.max_prediction_errors:
                    recent_errors = self.prediction_errors[-self.max_prediction_errors:]
                    if all(error > 10 for error in recent_errors):  # Todos > 10% error
                        return True, f"MUCHOS ERRORES: {self.max_prediction_errors} errores consecutivos > 10%"
            
            return False, "Calidad aceptable"
            
        except Exception as e:
            logger.error(f"Error evaluando calidad de predicción: {e}")
            return False, f"Error en evaluación: {e}"

#!/usr/bin/env python3
"""
Análisis de la Estrategia de Aprendizaje del Modelo LSTM
Este script analiza cómo el modelo procesa información y define su entrenamiento
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from src.ai.predictors.price_predictor import PricePredictor
from src.ai.indicators.technical_indicators import TechnicalIndicators
from src.services.binance_service import BinanceService
from src.config.settings import AI_CONFIG

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_data_processing():
    """Analizar cómo el modelo procesa los datos de entrada"""
    print("\n" + "="*80)
    print("📊 ANÁLISIS DEL PROCESAMIENTO DE DATOS")
    print("="*80)
    
    try:
        # Obtener datos históricos
        binance_service = BinanceService()
        end_time = datetime.now()
        start_time = end_time - timedelta(days=30)  # 30 días de datos
        
        print(f"📈 Obteniendo datos históricos de BTC (últimos 30 días)...")
        df = binance_service.get_historical_klines(
            symbol="BTCUSDT",
            interval="15m",
            start_time=start_time,
            end_time=end_time
        )
        
        print(f"   ✅ Datos obtenidos: {len(df)} registros")
        print(f"   📊 Rango de precios: ${df['close'].min():,.2f} - ${df['close'].max():,.2f}")
        
        # Analizar indicadores técnicos
        print("\n🔧 ANÁLISIS DE INDICADORES TÉCNICOS:")
        technical_indicators = TechnicalIndicators()
        
        # Calcular indicadores
        df_with_indicators = technical_indicators.calculate_all_indicators(df)
        feature_columns = technical_indicators.get_feature_columns()
        
        print(f"   📋 Indicadores calculados: {len(df_with_indicators.columns)} columnas totales")
        print(f"   🎯 Características seleccionadas: {len(feature_columns)} columnas")
        print(f"   📝 Lista de características:")
        for i, col in enumerate(feature_columns, 1):
            if col in df_with_indicators.columns:
                mean_val = df_with_indicators[col].mean()
                std_val = df_with_indicators[col].std()
                print(f"      {i:2d}. {col:15s} - Media: {mean_val:8.2f}, Std: {std_val:8.2f}")
            else:
                print(f"      {i:2d}. {col:15s} - ❌ NO DISPONIBLE")
        
        return df_with_indicators, feature_columns
        
    except Exception as e:
        print(f"❌ Error en análisis de procesamiento: {e}")
        return None, None

def analyze_sequence_creation(df, feature_columns):
    """Analizar cómo se crean las secuencias para el LSTM"""
    print("\n" + "="*80)
    print("🔄 ANÁLISIS DE CREACIÓN DE SECUENCIAS")
    print("="*80)
    
    try:
        sequence_length = AI_CONFIG.get("sequence_length", 30)
        print(f"📏 Longitud de secuencia configurada: {sequence_length} períodos")
        print(f"⏱️  Con intervalos de 15 minutos = {sequence_length * 15} minutos = {sequence_length * 15 / 60:.1f} horas")
        
        # Simular preparación de datos como lo hace el modelo
        features = df[feature_columns].copy()
        target = df['close'].values
        
        print(f"\n📊 DATOS PARA SECUENCIAS:")
        print(f"   📈 Registros totales: {len(df)}")
        print(f"   🎯 Características: {len(feature_columns)}")
        print(f"   📉 Secuencias posibles: {len(df) - sequence_length}")
        
        # Analizar algunas secuencias de ejemplo
        print(f"\n🔍 ANÁLISIS DE SECUENCIAS DE EJEMPLO:")
        
        for i in range(min(3, len(df) - sequence_length)):
            start_idx = sequence_length + i
            sequence_data = features.iloc[i:start_idx]
            target_price = target[start_idx]
            
            price_range = sequence_data['close']
            price_change = ((price_range.iloc[-1] - price_range.iloc[0]) / price_range.iloc[0]) * 100
            
            print(f"   Secuencia {i+1}:")
            print(f"      📊 Rango de precios: ${price_range.min():,.2f} - ${price_range.max():,.2f}")
            print(f"      📈 Cambio en secuencia: {price_change:+.2f}%")
            print(f"      🎯 Precio objetivo: ${target_price:,.2f}")
            
            # Analizar indicadores clave en la secuencia
            rsi_trend = "Alcista" if sequence_data['rsi'].iloc[-1] > sequence_data['rsi'].iloc[0] else "Bajista"
            macd_trend = "Alcista" if sequence_data['macd'].iloc[-1] > sequence_data['macd'].iloc[0] else "Bajista"
            
            print(f"      📊 RSI tendencia: {rsi_trend} ({sequence_data['rsi'].iloc[0]:.1f} → {sequence_data['rsi'].iloc[-1]:.1f})")
            print(f"      📈 MACD tendencia: {macd_trend} ({sequence_data['macd'].iloc[0]:.3f} → {sequence_data['macd'].iloc[-1]:.3f})")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error en análisis de secuencias: {e}")
        return False

def analyze_learning_patterns():
    """Analizar qué patrones aprende el modelo"""
    print("\n" + "="*80)
    print("🧠 ANÁLISIS DE PATRONES DE APRENDIZAJE")
    print("="*80)
    
    try:
        # Cargar modelo entrenado
        predictor = PricePredictor()
        
        print("📚 ESTRATEGIA DE APRENDIZAJE DEL MODELO:")
        print("\n1. 🔄 PROCESAMIENTO SECUENCIAL:")
        print("   - El modelo LSTM procesa secuencias de 30 períodos (7.5 horas)")
        print("   - Cada período contiene 12 características técnicas")
        print("   - Aprende patrones temporales en ventanas deslizantes")
        
        print("\n2. 📊 CARACTERÍSTICAS QUE ANALIZA:")
        feature_columns = [
            'open', 'high', 'low', 'close', 'volume',  # Datos básicos OHLCV
            'pivot',           # Punto de pivote (mayor correlación 0.997)
            'sma_50',          # Media móvil 50 (tendencia estable 0.843)
            'rsi',             # RSI (momentum 0.565)
            'stoch_d',         # Estocástico D (momentum 0.389)
            'macd',            # MACD (tendencia 0.291)
            'adx',             # ADX (fuerza de tendencia 0.204)
            'macd_histogram'   # MACD Histogram (cambios de momentum 0.153)
        ]
        
        for i, feature in enumerate(feature_columns, 1):
            if feature in ['open', 'high', 'low', 'close', 'volume']:
                print(f"   {i:2d}. {feature:15s} - Datos básicos de precio/volumen")
            elif feature == 'pivot':
                print(f"   {i:2d}. {feature:15s} - Nivel de soporte/resistencia clave")
            elif feature == 'sma_50':
                print(f"   {i:2d}. {feature:15s} - Tendencia a medio plazo")
            elif feature == 'rsi':
                print(f"   {i:2d}. {feature:15s} - Momentum (sobrecompra/sobreventa)")
            elif feature == 'stoch_d':
                print(f"   {i:2d}. {feature:15s} - Momentum suavizado")
            elif feature == 'macd':
                print(f"   {i:2d}. {feature:15s} - Convergencia/divergencia de medias")
            elif feature == 'adx':
                print(f"   {i:2d}. {feature:15s} - Fuerza de la tendencia")
            elif feature == 'macd_histogram':
                print(f"   {i:2d}. {feature:15s} - Cambios en momentum")
        
        print("\n3. 🎯 ESTRATEGIA DE PREDICCIÓN:")
        print("   - Identifica PATRONES TEMPORALES en las 12 características")
        print("   - Busca CORRELACIONES entre indicadores técnicos")
        print("   - Aprende SECUENCIAS que preceden a movimientos de precio")
        print("   - Predice el PRÓXIMO PRECIO basado en patrones históricos")
        
        print("\n4. 🔍 DETECCIÓN DE TENDENCIAS:")
        print("   📈 TENDENCIA ALCISTA detectada cuando:")
        print("      - SMA_50 en pendiente ascendente")
        print("      - RSI entre 50-70 (momentum positivo sin sobrecompra)")
        print("      - MACD > 0 y creciente")
        print("      - ADX > 25 (tendencia fuerte)")
        print("      - Precio por encima del pivot")
        
        print("   📉 TENDENCIA BAJISTA detectada cuando:")
        print("      - SMA_50 en pendiente descendente")
        print("      - RSI entre 30-50 (momentum negativo sin sobreventa)")
        print("      - MACD < 0 y decreciente")
        print("      - ADX > 25 (tendencia fuerte)")
        print("      - Precio por debajo del pivot")
        
        print("\n5. ⚖️ NORMALIZACIÓN Y ESCALADO:")
        print("   - Usa MinMaxScaler (0-1) para mayor sensibilidad")
        print("   - Normaliza cada característica independientemente")
        print("   - Mantiene relaciones proporcionales entre indicadores")
        
        print("\n6. 🏗️ ARQUITECTURA DE APRENDIZAJE:")
        print("   - 3 capas LSTM (128→64→32 neuronas)")
        print("   - BatchNormalization para estabilidad")
        print("   - Dropout (10-15%) para evitar overfitting")
        print("   - Función de pérdida Huber (robusta a outliers)")
        print("   - Optimizador Adam con learning rate adaptativo")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en análisis de patrones: {e}")
        return False

def analyze_prediction_logic():
    """Analizar la lógica específica de predicción"""
    print("\n" + "="*80)
    print("🎯 ANÁLISIS DE LÓGICA DE PREDICCIÓN")
    print("="*80)
    
    print("🧮 PROCESO DE PREDICCIÓN PASO A PASO:")
    
    print("\n1. 📥 ENTRADA DE DATOS:")
    print("   - Recibe últimos 30 períodos de datos (7.5 horas)")
    print("   - Cada período: 12 características técnicas")
    print("   - Forma de entrada: (1, 30, 12) - batch_size=1, secuencia=30, features=12")
    
    print("\n2. 🔄 PROCESAMIENTO LSTM:")
    print("   - Capa LSTM 1: Procesa secuencia completa, extrae patrones temporales")
    print("   - Capa LSTM 2: Refina patrones, reduce dimensionalidad")
    print("   - Capa LSTM 3: Consolida información en vector final")
    print("   - Cada capa tiene 'memoria' de estados anteriores")
    
    print("\n3. 🧠 EXTRACCIÓN DE PATRONES:")
    print("   - Identifica SECUENCIAS REPETITIVAS en los indicadores")
    print("   - Detecta CORRELACIONES entre diferentes características")
    print("   - Aprende TIMING de cambios de tendencia")
    print("   - Reconoce PATRONES DE MOMENTUM")
    
    print("\n4. 📊 EJEMPLOS DE PATRONES APRENDIDOS:")
    print("   📈 Patrón Alcista Típico:")
    print("      - RSI: 45→55→65 (momentum creciente)")
    print("      - MACD: negativo→cruce→positivo")
    print("      - SMA_50: precio rompe al alza")
    print("      - ADX: >25 (confirma fuerza)")
    print("      → PREDICCIÓN: Precio al alza")
    
    print("   📉 Patrón Bajista Típico:")
    print("      - RSI: 55→45→35 (momentum decreciente)")
    print("      - MACD: positivo→cruce→negativo")
    print("      - SMA_50: precio rompe a la baja")
    print("      - ADX: >25 (confirma fuerza)")
    print("      → PREDICCIÓN: Precio a la baja")
    
    print("\n5. 🎲 GENERACIÓN DE PREDICCIÓN:")
    print("   - Capas Dense procesan vector LSTM final")
    print("   - Salida: Valor normalizado (0-1)")
    print("   - Desnormalización: Convierte a precio real")
    print("   - Validación: Verifica rango razonable")
    
    print("\n6. 🔍 CÁLCULO DE CONFIANZA:")
    print("   - Basado en CONSISTENCIA de patrones")
    print("   - Mayor confianza si múltiples indicadores coinciden")
    print("   - Menor confianza en mercados laterales (ADX bajo)")
    print("   - Ajustada por VOLATILIDAD reciente")
    
    print("\n7. ⚠️ LIMITACIONES IDENTIFICADAS:")
    print("   - Entrenado con datos históricos específicos ($74K-$124K)")
    print("   - Sesgo hacia valores centrales del rango de entrenamiento")
    print("   - Función Huber favorece predicciones conservadoras")
    print("   - Dificultad para predecir movimientos extremos")

def main():
    """Función principal"""
    print("🚀 INICIANDO ANÁLISIS COMPLETO DE ESTRATEGIA DE APRENDIZAJE")
    print(f"⏰ Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. Analizar procesamiento de datos
    df, feature_columns = analyze_data_processing()
    
    if df is not None and feature_columns is not None:
        # 2. Analizar creación de secuencias
        analyze_sequence_creation(df, feature_columns)
    
    # 3. Analizar patrones de aprendizaje
    analyze_learning_patterns()
    
    # 4. Analizar lógica de predicción
    analyze_prediction_logic()
    
    print("\n" + "="*80)
    print("✅ ANÁLISIS COMPLETADO")
    print("="*80)
    print("\n📋 RESUMEN EJECUTIVO:")
    print("   🎯 El modelo usa 12 indicadores técnicos en secuencias de 30 períodos")
    print("   🧠 Aprende patrones temporales y correlaciones entre indicadores")
    print("   📈 Identifica tendencias mediante combinación de momentum y dirección")
    print("   ⚖️ Usa normalización MinMax y arquitectura LSTM de 3 capas")
    print("   🎲 Predice precios basándose en patrones históricos similares")
    print("   ⚠️ Limitado por rango de datos de entrenamiento y sesgo conservador")

if __name__ == "__main__":
    main()
"""Servicio para procesamiento y validación de datos
Separa la lógica de limpieza de datos del modelo LSTM
"""
import numpy as np
import pandas as pd
import logging
from typing import Tuple, Optional

logger = logging.getLogger(__name__)

class DataProcessor:
    """Procesador de datos para validación y limpieza"""
    
    def __init__(self):
        """Inicializar procesador de datos"""
        self.logger = logging.getLogger(__name__)
    
    def validate_and_clean_data(self, df: pd.DataFrame, target_column: str = 'close') -> pd.DataFrame:
        """Validar y limpiar datos con validaciones robustas
        
        Args:
            df: DataFrame con datos
            target_column: Columna objetivo (precio)
            
        Returns:
            DataFrame limpio y validado
        """
        try:
            self.logger.info(f"Iniciando validación de datos: {len(df)} filas, {len(df.columns)} columnas")
            
            # Verificar que hay suficientes datos
            if len(df) < 31:  # sequence_length + 1
                raise ValueError(f"Se necesitan al menos 31 puntos de datos")
            
            # VALIDACIÓN 1: Verificar que la columna objetivo existe y tiene datos válidos
            if target_column not in df.columns:
                raise ValueError(f"Columna objetivo '{target_column}' no encontrada en los datos")
            
            df_clean = df.copy()
            target = df_clean[target_column].copy()
            
            # VALIDACIÓN 2: Detectar y limpiar precios anómalos
            original_count = len(target)
            target_clean = target.dropna()
            
            if len(target_clean) == 0:
                raise ValueError(f"Columna objetivo '{target_column}' no tiene valores válidos")
            
            # Detectar outliers extremos en precios usando IQR
            Q1 = target_clean.quantile(0.25)
            Q3 = target_clean.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 3 * IQR  # Usar 3*IQR en lugar de 1.5 para ser menos restrictivo
            upper_bound = Q3 + 3 * IQR
            
            # Validar rangos razonables para precios de criptomonedas
            min_reasonable_price = 1.0  # $1 mínimo
            max_reasonable_price = 1000000.0  # $1M máximo
            
            # Aplicar límites combinados
            final_lower = max(lower_bound, min_reasonable_price)
            final_upper = min(upper_bound, max_reasonable_price)
            
            outliers_mask = (target < final_lower) | (target > final_upper)
            outliers_count = outliers_mask.sum()
            
            if outliers_count > 0:
                self.logger.warning(f"OUTLIERS_DETECTADOS: {outliers_count} precios anómalos encontrados")
                self.logger.info(f"Rango válido: ${final_lower:.2f} - ${final_upper:.2f}")
                self.logger.info(f"Outliers: min={target[outliers_mask].min():.2f}, max={target[outliers_mask].max():.2f}")
                
                # Reemplazar outliers con valores interpolados
                target_clean = target.copy()
                target_clean[outliers_mask] = np.nan
                target_clean = target_clean.interpolate(method='linear').fillna(method='ffill').fillna(method='bfill')
                
                self.logger.info(f"Outliers corregidos mediante interpolación")
                df_clean[target_column] = target_clean
            else:
                self.logger.info(f"No se detectaron outliers en precios")
            
            # VALIDACIÓN 3: Verificar cambios porcentuales extremos
            price_changes = df_clean[target_column].pct_change().dropna()
            extreme_changes = (abs(price_changes) > 0.5).sum()  # Cambios >50%
            
            if extreme_changes > 0:
                self.logger.warning(f"CAMBIOS_EXTREMOS: {extreme_changes} cambios de precio >50% detectados")
                # Suavizar cambios extremos
                extreme_mask = abs(price_changes) > 0.5
                for idx in price_changes[extreme_mask].index:
                    if idx > 0:
                        # Promediar con valores adyacentes
                        prev_val = df_clean[target_column].iloc[idx-1]
                        df_clean.loc[idx, target_column] = prev_val * (1 + np.sign(price_changes.iloc[idx-1]) * 0.1)  # Limitar a 10%
                
                self.logger.info(f"Cambios extremos suavizados")
            
            # VALIDACIÓN 4: Limpiar características
            self.logger.info(f"Validando características")
            
            # Eliminar columnas completamente vacías
            df_clean = df_clean.dropna(axis=1, how='all')
            
            # Detectar y corregir valores infinitos
            inf_cols = df_clean.columns[df_clean.isin([np.inf, -np.inf]).any()]
            if len(inf_cols) > 0:
                self.logger.warning(f"Valores infinitos detectados en: {list(inf_cols)}")
                df_clean = df_clean.replace([np.inf, -np.inf], np.nan)
            
            # Rellenar NaN con estrategia inteligente
            df_clean = df_clean.ffill().fillna(0)
            
            # VALIDACIÓN 5: Verificar rangos de características
            features_to_remove = []
            for col in df_clean.columns:
                if col == target_column or col == 'timestamp':
                    continue
                    
                col_data = df_clean[col]
                if col_data.std() == 0:
                    self.logger.warning(f"Característica '{col}' tiene varianza cero, se eliminará")
                    features_to_remove.append(col)
                elif abs(col_data).max() > 1e6:
                    self.logger.warning(f"Característica '{col}' tiene valores extremos, aplicando clipping")
                    df_clean[col] = col_data.clip(-1e6, 1e6)
            
            # Eliminar características problemáticas
            if features_to_remove:
                df_clean = df_clean.drop(columns=features_to_remove)
            
            self.logger.info(f"Datos validados exitosamente: {len(df_clean)} filas, {len(df_clean.columns)} columnas")
            self.logger.info(f"Rango de precios final: ${df_clean[target_column].min():.2f} - ${df_clean[target_column].max():.2f}")
            
            return df_clean
            
        except Exception as e:
            self.logger.error(f"Error validando datos: {e}")
            raise
    
    def basic_validation(self, df: pd.DataFrame, target_column: str = 'close') -> pd.DataFrame:
        """Validación básica más simple para casos donde no se necesita limpieza agresiva
        
        Args:
            df: DataFrame con datos
            target_column: Columna objetivo
            
        Returns:
            DataFrame con validación básica
        """
        try:
            self.logger.info(f"Validación básica: {len(df)} filas")
            
            df_clean = df.copy()
            
            # Solo eliminar NaN y valores infinitos
            df_clean = df_clean.replace([np.inf, -np.inf], np.nan)
            df_clean = df_clean.dropna()
            
            # Verificar que queden suficientes datos
            if len(df_clean) < 31:
                raise ValueError("Insuficientes datos después de limpieza básica")
            
            self.logger.info(f"Validación básica completada: {len(df_clean)} filas")
            return df_clean
            
        except Exception as e:
            self.logger.error(f"Error en validación básica: {e}")
            raise
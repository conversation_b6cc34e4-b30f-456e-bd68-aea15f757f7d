"""
Servicio para procesar y transformar datos del dashboard
"""
import logging
from typing import Dict, List, Optional, Tuple
import pandas as pd
import numpy as np
import logging

from config.settings import CHART_CONFIG
from config.constants import DATE_FORMATS

logger = logging.getLogger(__name__)

class DataService:
    """Servicio para procesar y transformar datos"""
    
    def __init__(self):
        """Inicializar el servicio de datos"""
        self.config = CHART_CONFIG
        self._setup_logging()
    
    def _setup_logging(self):
        """Configurar logging para el servicio"""
        logger.setLevel(logging.INFO)
    
    def process_market_data(self, df: pd.DataFrame) -> Dict:
        """Procesar datos del mercado y calcular métricas"""
        try:
            if df.empty:
                return {}
            
            current_price = df['close'].iloc[-1]
            volume_total = df['volume'].sum() if 'volume' in df.columns else 0
            
            # Calcular cambio de precio
            if len(df) > 1:
                price_change = current_price - df['close'].iloc[-2]
                price_change_pct = (price_change / df['close'].iloc[-2]) * 100
            else:
                price_change = 0
                price_change_pct = 0
            
            # Calcular máximo y mínimo de 24h
            high_24h = df['high'].max()
            low_24h = df['low'].min()
            
            return {
                'current_price': current_price,
                'price_change': price_change,
                'price_change_pct': price_change_pct,
                'volume_total': volume_total,
                'volume_24h': volume_total,
                'high_24h': high_24h,
                'low_24h': low_24h,
                'last_update': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'recent_data': f"Últimos {len(df)} registros procesados"
            }
            
        except Exception as e:
            logger.error(f"Error procesando datos del mercado: {e}")
            return {}
    
    def _calculate_price_change_percent(self, df: pd.DataFrame) -> float:
        """Calcular cambio porcentual del precio"""
        try:
            if len(df) < 2:
                return 0.0
            initial_price = df['close'].iloc[0]
            final_price = df['close'].iloc[-1]
            return ((final_price - initial_price) / initial_price) * 100
        except Exception:
            return 0.0
    
    def _calculate_volatility(self, df: pd.DataFrame) -> float:
        """Calcular volatilidad del precio"""
        try:
            returns = df['close'].pct_change().dropna()
            return returns.std() * 100
        except Exception:
            return 0.0
    
    def _get_time_range(self, df: pd.DataFrame) -> Dict:
        """Obtener rango de tiempo de los datos"""
        try:
            if 'timestamp' not in df.columns:
                return {}
            return {
                'start': df['timestamp'].min(),
                'end': df['timestamp'].max(),
                'duration': df['timestamp'].max() - df['timestamp'].min()
            }
        except Exception:
            return {}
    
    def _get_recent_data(self, df: pd.DataFrame) -> List[Dict]:
        """Obtener datos recientes para la tabla"""
        try:
            recent_rows = self.config['recent_data_rows']
            
            # Verificar si existe la columna timestamp
            if 'timestamp' not in df.columns:
                # Si no existe timestamp, usar solo las columnas de precio y volumen
                columns = ['open', 'high', 'low', 'close']
                if 'volume' in df.columns:
                    columns.append('volume')
                recent_data = df.tail(recent_rows)[columns]
                
                return [
                    {
                        'timestamp': f"Registro {i+1}",
                        'open': f"${row['open']:.2f}",
                        'high': f"${row['high']:.2f}",
                        'low': f"${row['low']:.2f}",
                        'close': f"${row['close']:.2f}",
                        'volume': f"{row['volume']:,.0f}" if 'volume' in row else "N/A"
                    }
                    for i, (_, row) in enumerate(recent_data.iterrows())
                ]
            else:
                recent_data = df.tail(recent_rows)[['timestamp', 'open', 'high', 'low', 'close', 'volume']]
                
                return [
                    {
                        'timestamp': row['timestamp'].strftime(DATE_FORMATS['display']),
                        'open': f"${row['open']:.2f}",
                        'high': f"${row['high']:.2f}",
                        'low': f"${row['low']:.2f}",
                        'close': f"${row['close']:.2f}",
                        'volume': f"{row['volume']:,.0f}"
                    }
                    for _, row in recent_data.iterrows()
                ]
        except Exception as e:
            logger.error(f"Error obteniendo datos recientes: {e}")
            return []
    
    def format_price(self, price: float, decimals: int = 2) -> str:
        """Formatear precio para mostrar"""
        try:
            return f"${price:.{decimals}f}"
        except Exception:
            return "N/A"
    
    def format_volume(self, volume: float) -> str:
        """Formatear volumen para mostrar"""
        try:
            if volume >= 1_000_000:
                return f"{volume/1_000_000:.2f}M"
            elif volume >= 1_000:
                return f"{volume/1_000:.2f}K"
            else:
                return f"{volume:,.0f}"
        except Exception:
            return "N/A"

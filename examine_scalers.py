#!/usr/bin/env python3
"""
Script para examinar los scalers guardados y compararlos con datos actuales
"""

import pickle
import numpy as np
from pathlib import Path
import sys

# Configurar rutas
project_root = Path(__file__).parent
sys.path.append(str(project_root))
sys.path.append(str(project_root / "src"))

from services.binance_service import BinanceService
from ai.indicators.technical_indicators import TechnicalIndicators
from sklearn.preprocessing import StandardScaler

def examine_saved_scalers():
    """Examinar los scalers guardados"""
    print("🔍 EXAMINANDO SCALERS GUARDADOS")
    print("=" * 50)
    
    scaler_path = project_root / "models" / "btc_lstm_model_scalers.pkl"
    
    if not scaler_path.exists():
        print(f"❌ Archivo no encontrado: {scaler_path}")
        return None, None
    
    with open(scaler_path, 'rb') as f:
        scalers = pickle.load(f)
    
    print(f"📁 Claves en archivo: {list(scalers.keys())}")
    
    target_scaler = scalers.get('target_scaler')
    feature_scaler = scalers.get('feature_scaler')
    
    if target_scaler:
        print(f"\n🎯 TARGET SCALER:")
        print(f"   Tipo: {type(target_scaler).__name__}")
        print(f"   Mean: {target_scaler.mean_}")
        print(f"   Scale: {target_scaler.scale_}")
        print(f"   Var: {target_scaler.var_}")
        print(f"   N_features: {target_scaler.n_features_in_}")
        print(f"   N_samples: {target_scaler.n_samples_seen_}")
    
    if feature_scaler:
        print(f"\n📊 FEATURE SCALER:")
        print(f"   Tipo: {type(feature_scaler).__name__}")
        print(f"   N_features: {feature_scaler.n_features_in_}")
        print(f"   Feature names: {getattr(feature_scaler, 'feature_names_in_', 'No disponible')}")
        if hasattr(feature_scaler, 'mean_'):
            print(f"   Mean shape: {feature_scaler.mean_.shape}")
            print(f"   Scale shape: {feature_scaler.scale_.shape}")
    
    return target_scaler, feature_scaler

def calculate_fresh_target_stats():
    """Calcular estadísticas del target con datos frescos"""
    print("\n🆕 CALCULANDO ESTADÍSTICAS FRESCAS")
    print("=" * 50)
    
    # Obtener datos
    binance = BinanceService()
    data = binance.get_klines_data('BTCUSDT', '15m', limit=1000)
    
    if data is None or len(data) < 100:
        print("❌ Error obteniendo datos")
        return None
    
    # Calcular indicadores
    indicators = TechnicalIndicators()
    data_with_indicators = indicators.calculate_all_indicators(data)
    
    # Calcular targets ATR-escalados
    targets = []
    for i in range(len(data_with_indicators) - 12):  # 12 períodos hacia adelante
        current_price = data_with_indicators.iloc[i]['close']
        future_price = data_with_indicators.iloc[i + 12]['close']
        atr = data_with_indicators.iloc[i]['atr']
        
        if atr > 0:
            target = (future_price - current_price) / atr
            targets.append(target)
    
    targets = np.array(targets)
    
    print(f"📊 Estadísticas del target fresco:")
    print(f"   Samples: {len(targets)}")
    print(f"   Mean: {np.mean(targets):.6f}")
    print(f"   Std: {np.std(targets):.6f}")
    print(f"   Min: {np.min(targets):.6f}")
    print(f"   Max: {np.max(targets):.6f}")
    
    # Crear scaler fresco
    fresh_scaler = StandardScaler()
    targets_reshaped = targets.reshape(-1, 1)
    fresh_scaler.fit(targets_reshaped)
    
    print(f"\n🔧 Scaler fresco:")
    print(f"   Mean: {fresh_scaler.mean_[0]:.6f}")
    print(f"   Scale: {fresh_scaler.scale_[0]:.6f}")
    print(f"   Var: {fresh_scaler.var_[0]:.6f}")
    
    return fresh_scaler, targets

def compare_scalers(saved_scaler, fresh_scaler):
    """Comparar scalers guardado vs fresco"""
    print("\n⚖️  COMPARACIÓN DE SCALERS")
    print("=" * 50)
    
    if saved_scaler is None or fresh_scaler is None:
        print("❌ No se pueden comparar scalers")
        return
    
    saved_mean = saved_scaler.mean_[0]
    saved_scale = saved_scaler.scale_[0]
    fresh_mean = fresh_scaler.mean_[0]
    fresh_scale = fresh_scaler.scale_[0]
    
    mean_diff = abs(saved_mean - fresh_mean)
    scale_diff = abs(saved_scale - fresh_scale)
    
    print(f"📊 Diferencias:")
    print(f"   Mean: {mean_diff:.6f} ({mean_diff/abs(fresh_mean)*100:.2f}% relativo)")
    print(f"   Scale: {scale_diff:.6f} ({scale_diff/fresh_scale*100:.2f}% relativo)")
    
    # Probar transformaciones
    test_values = np.array([0.0, 0.5, -0.5, 1.0, -1.0]).reshape(-1, 1)
    
    print(f"\n🧪 Prueba de transformaciones:")
    print(f"{'Valor':<8} {'Guardado':<12} {'Fresco':<12} {'Diferencia':<12}")
    print("-" * 48)
    
    for val in test_values.flatten():
        val_reshaped = np.array([[val]])
        saved_result = saved_scaler.inverse_transform(val_reshaped)[0, 0]
        fresh_result = fresh_scaler.inverse_transform(val_reshaped)[0, 0]
        diff = abs(saved_result - fresh_result)
        
        print(f"{val:<8.2f} {saved_result:<12.6f} {fresh_result:<12.6f} {diff:<12.6f}")

def main():
    print("🔍 DIAGNÓSTICO COMPLETO DE SCALERS")
    print("=" * 60)
    
    # Examinar scalers guardados
    saved_target_scaler, saved_feature_scaler = examine_saved_scalers()
    
    # Calcular estadísticas frescas
    fresh_scaler, targets = calculate_fresh_target_stats()
    
    # Comparar
    compare_scalers(saved_target_scaler, fresh_scaler)
    
    print("\n✅ Diagnóstico completado")

if __name__ == "__main__":
    main()
from src.ai.predictors.price_predictor import PricePredictor
from src.services.binance_service import BinanceService
import pandas as pd
import logging

# Configurar logging para ver los detalles
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(name)s - %(message)s')

# Obtener datos
bs = BinanceService()
data = bs.get_klines_data('BTCUSDT', '1h', 100)

# Crear predictor y cargar modelo
predictor = PricePredictor()
predictor.load_model('models/btc_lstm_model.h5')

# Hacer predicción
predicted_price, confidence = predictor.predict_next_price(data)

# Mostrar resultados
print(f'Precio actual: {data["close"].iloc[-1]:.2f}')
print(f'Precio predicho: {predicted_price:.2f}')
print(f'Confianza: {confidence:.2%}')
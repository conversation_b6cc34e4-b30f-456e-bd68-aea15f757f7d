"""
Modelo LSTM para predicción de precios de criptomonedas
"""
import numpy as np
import pandas as pd
from typing import Tuple, Optional, Dict, Any
import logging
from datetime import datetime
import os
import pickle

from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.preprocessing import RobustScaler

# Importar configuración
try:
    import sys
    from pathlib import Path
    # Agregar src al path si no está
    src_path = Path(__file__).parent.parent.parent
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))
    from config.settings import AI_CONFIG
except ImportError:
    # Fallback si no se puede importar
    AI_CONFIG = {"sequence_length": 30}

logger = logging.getLogger(__name__)

class LSTMModel:
    """Modelo LSTM para predicción de precios"""
    
    def __init__(self, sequence_length: int = None, prediction_horizon: int = 1):
        """
        Inicializar modelo LSTM
        
        Args:
            sequence_length: Longitud de la secuencia de entrada (None usa configuración)
            prediction_horizon: Horizonte de predicción (1 = próximo punto)
        """
        # Usar configuración si no se especifica sequence_length
        if sequence_length is None:
            sequence_length = AI_CONFIG.get("sequence_length", 30)
        self.sequence_length = sequence_length
        self.prediction_horizon = prediction_horizon
        self.model = None
        self.scaler = StandardScaler()  # Para target ATR-escalado
        self.feature_scaler = StandardScaler()  # Para características - StandardScaler para mejor distribución
        self.is_trained = False
        self.training_history = None
        
        self._setup_logging()
    
    def _setup_logging(self):
        """Configurar logging"""
        logger.setLevel(logging.INFO)
    
    def create_model(self, input_shape: Tuple[int, int]) -> Sequential:
        """
        Crear arquitectura del modelo LSTM mejorada para evitar predicciones planas
        
        Args:
            input_shape: Forma de entrada (sequence_length, features)
            
        Returns:
            Modelo Keras compilado
        """
        try:
            # Arquitectura mejorada para evitar convergencia prematura
            model = Sequential([
                # Primera capa LSTM con dropout reducido
                LSTM(128, return_sequences=True, input_shape=input_shape,
                     recurrent_dropout=0.05, dropout=0.05),  # Dropout reducido
                BatchNormalization(),
                
                # Segunda capa LSTM
                LSTM(64, return_sequences=True, 
                     recurrent_dropout=0.05, dropout=0.05),
                BatchNormalization(),
                
                # Tercera capa LSTM
                LSTM(32, return_sequences=False,
                     recurrent_dropout=0.05, dropout=0.05),
                BatchNormalization(),
                
                # Capas densas con dropout reducido
                Dense(64, activation='relu'),
                Dropout(0.1),  # Dropout reducido
                Dense(32, activation='relu'),
                Dropout(0.05),  # Dropout reducido
                Dense(16, activation='relu'),
                Dropout(0.05),  # Dropout mínimo
                Dense(self.prediction_horizon, activation='linear')
            ])
            
            # Compilar modelo con MSE
            model.compile(
                optimizer=Adam(learning_rate=0.003, beta_1=0.9, beta_2=0.999, epsilon=1e-7),
                loss='mse',  # MSE para mayor sensibilidad a errores
                metrics=['mae', 'mse']
            )
            
            logger.info(f"Modelo LSTM mejorado creado: {input_shape[1]} características, {self.sequence_length} secuencia")
            return model
            
        except Exception as e:
            logger.error(f"Error creando modelo LSTM: {e}")
            raise
    
    def prepare_data(self, df: pd.DataFrame, target_column: str = 'close') -> Tuple[np.ndarray, np.ndarray]:
        """
        Preparar datos para entrenamiento con target escalado por ATR
        Target = (Close_{t+H} - Close_t) / ATR_14_t
        
        Args:
            df: DataFrame con características (ya validado)
            target_column: Columna objetivo (precio)
            
        Returns:
            Tupla con X (características) e y (objetivo ATR-escalado)
        """
        try:
            logger.info(f"Preparando datos para modelo: {len(df)} filas, {len(df.columns)} columnas")
            
            # Verificación básica
            if len(df) < self.sequence_length + self.prediction_horizon:
                raise ValueError(f"Se necesitan al menos {self.sequence_length + self.prediction_horizon} puntos de datos")
            
            if target_column not in df.columns:
                raise ValueError(f"Columna objetivo '{target_column}' no encontrada")
            
            if 'atr' not in df.columns:
                raise ValueError("Columna 'atr' no encontrada. Se requiere ATR para el target escalado")
            
            # Separar características (excluir close, atr y timestamp)
            exclude_cols = [target_column, 'atr']
            if 'timestamp' in df.columns:
                exclude_cols.append('timestamp')
            features = df.drop(columns=exclude_cols)
            
            # Calcular target escalado por ATR: y = (Close_{t+H} - Close_t) / ATR_14_t
            close_prices = df[target_column].values
            atr_values = df['atr'].values
            
            # Crear target ATR-escalado
            atr_scaled_target = []
            for i in range(len(close_prices) - self.prediction_horizon):
                future_price = close_prices[i + self.prediction_horizon]
                current_price = close_prices[i]
                current_atr = atr_values[i]
                
                # Evitar división por cero
                if current_atr > 0:
                    atr_return = (future_price - current_price) / current_atr
                else:
                    atr_return = 0.0
                
                atr_scaled_target.append(atr_return)
            
            atr_scaled_target = np.array(atr_scaled_target)
            
            # Normalizar características
            features_scaled = self.feature_scaler.fit_transform(features)
            
            # Normalizar target ATR-escalado
            target_scaled = self.scaler.fit_transform(atr_scaled_target.reshape(-1, 1)).flatten()
            
            # Crear secuencias
            X, y = [], []
            
            # Ajustar límites para el target ATR-escalado
            # target_scaled tiene menos elementos debido a prediction_horizon
            max_index = min(len(features_scaled) - self.prediction_horizon, len(target_scaled))
            
            for i in range(self.sequence_length, max_index):
                X.append(features_scaled[i-self.sequence_length:i])
                y.append(target_scaled[i-self.sequence_length])  # Índice correcto para target ATR-escalado
            
            X = np.array(X)
            y = np.array(y)
            
            if len(X) == 0 or len(y) == 0:
                raise ValueError("No se pudieron crear secuencias válidas")
            
            logger.info(f"Datos preparados con target ATR-escalado: X={X.shape}, y={y.shape}")
            logger.info(f"Rango del target ATR-escalado: [{atr_scaled_target.min():.4f}, {atr_scaled_target.max():.4f}]")
            return X, y
            
        except Exception as e:
            logger.error(f"Error preparando datos: {e}")
            raise
    
    def train(self, df: pd.DataFrame, 
              target_column: str = 'close',
              validation_split: float = 0.2,
              epochs: int = 100,  # Más épocas para mejor aprendizaje
              batch_size: int = 32) -> Dict[str, Any]:
        """
        Entrenar el modelo LSTM
        
        Args:
            df: DataFrame con características
            target_column: Columna objetivo
            validation_split: Porcentaje de datos de validación
            epochs: Número de épocas
            batch_size: Tamaño del batch
            
        Returns:
            Diccionario con historial de entrenamiento
        """
        try:
            logger.info("Iniciando entrenamiento del modelo LSTM...")
            
            # Preparar datos
            X, y = self.prepare_data(df, target_column)
            
            # Crear modelo
            input_shape = (X.shape[1], X.shape[2])
            self.model = self.create_model(input_shape)
            
            # Callbacks balanceados para estabilidad y aprendizaje
            callbacks = [
                EarlyStopping(
                    monitor='val_loss',
                    patience=12,  # Paciencia moderada
                    restore_best_weights=True,
                    verbose=1,
                    min_delta=0.001  # Delta balanceado
                ),
                ReduceLROnPlateau(
                    monitor='val_loss',
                    factor=0.5,  # Reducción moderada
                    patience=6,  # Paciencia moderada
                    min_lr=1e-6,
                    verbose=1,
                    cooldown=3  # Menos tiempo de espera
                )
            ]
            
            # Entrenar modelo
            history = self.model.fit(
                X, y,
                validation_split=validation_split,
                epochs=epochs,
                batch_size=batch_size,
                callbacks=callbacks,
                verbose=1
            )
            
            self.is_trained = True
            self.training_history = history.history
            
            logger.info("Modelo LSTM entrenado exitosamente")
            return history.history
            
        except Exception as e:
            logger.error(f"Error entrenando modelo: {e}")
            raise
    
    def predict(self, df: pd.DataFrame, target_column: str = 'close') -> float:
        """
        Realizar predicción con desescalado ATR: pred_price = Close_t + y_pred * ATR_14_t
        
        Args:
            df: DataFrame con las últimas sequence_length filas de datos
            target_column: Columna objetivo para normalización
            
        Returns:
            Predicción del precio usando desescalado ATR
        """
        try:
            if self.model is None:
                raise ValueError("Modelo no entrenado. Ejecutar train() primero")
            
            if len(df) < self.sequence_length:
                raise ValueError(f"Se necesitan al menos {self.sequence_length} filas para predicción")
            
            if target_column not in df.columns:
                raise ValueError(f"Columna objetivo '{target_column}' no encontrada")
                
            if 'atr' not in df.columns:
                raise ValueError("Columna 'atr' no encontrada. Se requiere ATR para desescalar")
            
            # Tomar las últimas sequence_length filas
            recent_data = df.tail(self.sequence_length).copy()
            
            # Obtener precio actual y ATR actual (última fila)
            current_price = recent_data[target_column].iloc[-1]
            current_atr = recent_data['atr'].iloc[-1]
            
            # Preparar características (excluir close, atr y timestamp)
            exclude_cols = [target_column, 'atr']
            if 'timestamp' in recent_data.columns:
                exclude_cols.append('timestamp')
            features = recent_data.drop(columns=exclude_cols)
            
            # Verificar que el scaler esté entrenado
            if not hasattr(self.feature_scaler, 'scale_'):
                raise ValueError("Feature scaler no está entrenado")
            
            # Escalar características
            try:
                features_scaled = self.feature_scaler.transform(features)
                features_scaled = np.nan_to_num(features_scaled, 0)  # Manejar NaN
            except Exception as e:
                logger.error(f"Error escalando características: {e}")
                raise ValueError(f"Error en escalado de características: {e}")
            
            # Preparar entrada para el modelo (reshape a 3D para LSTM)
            X_pred = features_scaled.reshape(1, self.sequence_length, features_scaled.shape[1])
            
            # Realizar predicción (obtiene retorno ATR-escalado normalizado)
            prediction_scaled = self.model.predict(X_pred, verbose=0)
            
            # Extraer valor de predicción
            if prediction_scaled.ndim > 1:
                pred_value = prediction_scaled[0, 0] if prediction_scaled.shape[1] == 1 else prediction_scaled[0]
            else:
                pred_value = prediction_scaled[0]
            
            # Asegurar que pred_value sea escalar
            if hasattr(pred_value, '__len__') and len(pred_value) > 0:
                pred_value = pred_value[0]
            
            # Desnormalizar el retorno ATR-escalado
            pred_array = np.array([[float(pred_value)]])
            atr_scaled_return = self.scaler.inverse_transform(pred_array)[0, 0]
            
            # Desescalar usando ATR: pred_price = Close_t + y_pred * ATR_14_t
            if current_atr > 0:
                predicted_price = current_price + (atr_scaled_return * current_atr)
            else:
                predicted_price = current_price  # Si ATR es 0, no hay cambio
            
            logger.debug(f"Predicción ATR: current_price={current_price:.2f}, atr={current_atr:.4f}, "
                        f"atr_return={atr_scaled_return:.4f}, pred_price={predicted_price:.2f}")
            
            return float(predicted_price)
            
        except Exception as e:
            logger.error(f"Error en predicción: {e}")
            raise
    

    
    def predict_from_features(self, features: np.ndarray, current_price: float = None, current_atr: float = None) -> float:
        """
        Realizar predicción desde características ya preparadas.
        IMPORTANTE: El modelo predice retornos escalados por ATR, NO precios directos.
        
        Args:
            features: Numpy array con shape (1, sequence_length, num_features)
            current_price: Precio actual para desescalar (requerido)
            current_atr: ATR actual para desescalar (requerido)
            
        Returns:
            Predicción del precio usando desescalado ATR: pred_price = Close_t + y_pred * ATR_14_t
        """
        try:
            if self.model is None:
                raise ValueError("Modelo no entrenado.")
            
            if current_price is None or current_atr is None:
                raise ValueError("Se requieren current_price y current_atr para desescalar correctamente")
            
            # Validar dimensiones de entrada
            if len(features.shape) != 3:
                raise ValueError(f"Features debe tener 3 dimensiones (batch, sequence, features), recibido: {features.shape}")
            
            if features.shape[1] != self.sequence_length:
                raise ValueError(f"Sequence length debe ser {self.sequence_length}, recibido: {features.shape[1]}")
            
            # Realizar predicción
            prediction_scaled = self.model.predict(features, verbose=0)
            
            # Manejar diferentes formas de salida del modelo
            if len(prediction_scaled.shape) > 1:
                pred_value = prediction_scaled[0]
            else:
                pred_value = prediction_scaled
            
            # Para múltiples horizontes de predicción
            if self.prediction_horizon > 1 and hasattr(pred_value, '__len__'):
                pred_value = pred_value[0]
            
            # Convertir a escalar
            y_pred_normalized = float(np.asarray(pred_value).flatten()[0])
            
            # DESESCALAR USANDO TARGET SCALER (retornos escalados por ATR)
            if hasattr(self, 'target_scaler') and self.target_scaler is not None:
                # Desnormalizar el retorno escalado por ATR
                y_pred_array = np.array([[y_pred_normalized]])
                y_pred_atr_scaled = self.target_scaler.inverse_transform(y_pred_array)[0][0]
            else:
                # Si no hay target_scaler, asumir que ya está en escala ATR
                y_pred_atr_scaled = y_pred_normalized
            
            # DESESCALAR USANDO ATR: pred_price = Close_t + y_pred * ATR_14_t
            predicted_price = current_price + (y_pred_atr_scaled * current_atr)
            
            # DEBUG: Logging detallado para diagnóstico
            logger.info(f"🔍 DIAGNÓSTICO PREDICCIÓN ATR:")
            logger.info(f"   Precio actual: {current_price:.2f}")
            logger.info(f"   ATR actual: {current_atr:.4f}")
            logger.info(f"   y_pred normalizado: {y_pred_normalized:.6f}")
            logger.info(f"   y_pred escalado ATR: {y_pred_atr_scaled:.6f}")
            logger.info(f"   Cambio predicho: {y_pred_atr_scaled * current_atr:.2f}")
            logger.info(f"   Precio predicho final: {predicted_price:.2f}")
            
            # Validación de rango razonable
            change_pct = ((predicted_price - current_price) / current_price) * 100
            if abs(change_pct) > 15:
                logger.warning(f"⚠️  CAMBIO EXTREMO PREDICHO: {change_pct:.2f}%")
            
            return float(predicted_price)
            
        except Exception as e:
            logger.error(f"Error en predicción desde características: {e}")
            logger.error(f"Features shape: {features.shape if 'features' in locals() else 'undefined'}")
            logger.error(f"Model input shape esperado: (batch_size, {self.sequence_length}, num_features)")
            raise

    def evaluate(self, df: pd.DataFrame, target_column: str = 'close') -> Dict[str, float]:
        """
        Evaluar el modelo con métricas
        
        Args:
            df: DataFrame con características
            target_column: Columna objetivo
            
        Returns:
            Diccionario con métricas de evaluación
        """
        try:
            if not self.is_trained or self.model is None:
                raise ValueError("El modelo debe estar entrenado antes de evaluar")
            
            # Preparar datos
            X, y = self.prepare_data(df, target_column)
            
            # Hacer predicciones
            predictions_scaled = self.model.predict(X, verbose=0)
            
            # Desnormalizar predicciones y valores reales
            predictions = self.scaler.inverse_transform(predictions_scaled.reshape(-1, 1)).flatten()
            actuals = self.scaler.inverse_transform(y.reshape(-1, 1)).flatten()
            
            # Calcular métricas
            mse = mean_squared_error(actuals, predictions)
            mae = mean_absolute_error(actuals, predictions)
            rmse = np.sqrt(mse)
            
            # Calcular precisión direccional
            directional_accuracy = np.mean(
                np.sign(np.diff(actuals)) == np.sign(np.diff(predictions))
            )
            
            metrics = {
                'mse': mse,
                'mae': mae,
                'rmse': rmse,
                'directional_accuracy': directional_accuracy
            }
            
            logger.info(f"Métricas de evaluación: {metrics}")
            return metrics
            
        except Exception as e:
            logger.error(f"Error evaluando modelo: {e}")
            raise
    
    def save_model(self, filepath: str):
        """Guardar modelo entrenado"""
        try:
            if self.model is not None:
                self.model.save(filepath)
                
                # Guardar scalers
                scaler_path = filepath.replace('.h5', '_scalers.pkl')
                with open(scaler_path, 'wb') as f:
                    pickle.dump({
                        'target_scaler': self.scaler,  # Scaler para target ATR-escalado
                        'feature_scaler': self.feature_scaler
                    }, f)
                
                logger.info(f"Modelo guardado en: {filepath}")
            else:
                logger.warning("No hay modelo para guardar")
                
        except Exception as e:
            logger.error(f"Error guardando modelo: {e}")
            raise
    
    def load_model(self, filepath: str):
        """Cargar modelo guardado"""
        try:
            self.model = load_model(filepath)
            
            # Cargar scalers
            scaler_path = filepath.replace('.h5', '_scalers.pkl')
            if os.path.exists(scaler_path):
                with open(scaler_path, 'rb') as f:
                    scalers = pickle.load(f)
                    
                    # Validar que existan las claves necesarias
                    if 'target_scaler' not in scalers or 'feature_scaler' not in scalers:
                        raise ValueError(f"Archivo de scalers incompleto. Claves encontradas: {list(scalers.keys())}")
                    
                    # Usar target_scaler para el target ATR-escalado
                    self.scaler = scalers['target_scaler']
                    self.feature_scaler = scalers['feature_scaler']
                    logger.info("Scalers cargados correctamente")
            else:
                logger.warning(f"Archivo de scalers no encontrado: {scaler_path}")
                logger.warning("Se usarán scalers por defecto (pueden causar predicciones incorrectas)")
                # Inicializar scalers por defecto
                self.scaler = RobustScaler()
                self.feature_scaler = RobustScaler()
            
            self.is_trained = True
            logger.info(f"Modelo cargado desde: {filepath}")
            
        except Exception as e:
            logger.error(f"Error cargando modelo: {e}")
            raise
    
    def get_model_summary(self) -> str:
        """Obtener resumen del modelo"""
        if self.model is not None:
            return self.model.summary()
        return "Modelo no creado"

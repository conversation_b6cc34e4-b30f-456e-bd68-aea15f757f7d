#!/usr/bin/env python3
"""
Script para diagnosticar el problema del scaler ATR
"""

import sys
from pathlib import Path

# Agregar el directorio src al path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(src_path))

import pandas as pd
import numpy as np
import pickle
import os

# Importar desde la estructura correcta
sys.path.append(str(project_root / "src"))
from services.binance_service import BinanceService
from ai.indicators.technical_indicators import TechnicalIndicators
from ai.models.lstm_model import LSTMModel

def analyze_atr_target_calculation():
    """<PERSON><PERSON><PERSON> cómo se calcula el target ATR-escalado"""
    print("🔍 ANÁLISIS DEL CÁLCULO DEL TARGET ATR-ESCALADO")
    print("=" * 60)
    
    # 1. Obtener datos reales
    print("1. Obteniendo datos de Binance...")
    binance = BinanceService()
    df = binance.get_klines_data('BTCUSDT', '15m', 200)
    
    print(f"   📊 Datos obtenidos: {len(df)} filas")
    print(f"   💰 Precio actual: ${df['close'].iloc[-1]:,.2f}")
    print(f"   📈 Rango de precios: ${df['close'].min():,.2f} - ${df['close'].max():,.2f}")
    
    # 2. Calcular indicadores técnicos
    print("\n2. Calculando indicadores técnicos...")
    tech_indicators = TechnicalIndicators()
    df_with_indicators = tech_indicators.calculate_all_indicators(df)
    
    print(f"   ✅ ATR calculado")
    print(f"   📊 ATR promedio: {df_with_indicators['atr'].mean():.4f}")
    print(f"   📊 ATR rango: [{df_with_indicators['atr'].min():.4f}, {df_with_indicators['atr'].max():.4f}]")
    
    # 3. Simular cálculo del target ATR-escalado manualmente
    print("\n3. Calculando target ATR-escalado manualmente...")
    close_prices = df_with_indicators['close'].values
    atr_values = df_with_indicators['atr'].values
    prediction_horizon = 1
    
    atr_scaled_targets = []
    for i in range(len(close_prices) - prediction_horizon):
        future_price = close_prices[i + prediction_horizon]
        current_price = close_prices[i]
        current_atr = atr_values[i]
        
        if current_atr > 0:
            atr_return = (future_price - current_price) / current_atr
        else:
            atr_return = 0.0
        
        atr_scaled_targets.append(atr_return)
    
    atr_scaled_targets = np.array(atr_scaled_targets)
    
    print(f"   📊 Target ATR-escalado calculado: {len(atr_scaled_targets)} valores")
    print(f"   📊 Rango: [{atr_scaled_targets.min():.4f}, {atr_scaled_targets.max():.4f}]")
    print(f"   📊 Media: {atr_scaled_targets.mean():.4f}")
    print(f"   📊 Std: {atr_scaled_targets.std():.4f}")
    
    # 4. Mostrar algunos ejemplos
    print("\n4. Ejemplos de cálculo:")
    for i in range(min(5, len(atr_scaled_targets))):
        idx = -(i+1)  # Últimos valores
        future_price = close_prices[idx + prediction_horizon] if idx + prediction_horizon < len(close_prices) else close_prices[idx]
        current_price = close_prices[idx]
        current_atr = atr_values[idx]
        atr_return = atr_scaled_targets[idx]
        
        print(f"   Ejemplo {i+1}:")
        print(f"      Precio actual: ${current_price:,.2f}")
        print(f"      Precio futuro: ${future_price:,.2f}")
        print(f"      ATR: {current_atr:.4f}")
        print(f"      Cambio absoluto: ${future_price - current_price:,.2f}")
        print(f"      Target ATR-escalado: {atr_return:.4f}")
        print()
    
    return df_with_indicators, atr_scaled_targets

def analyze_scaler_behavior(atr_scaled_targets):
    """Analizar el comportamiento del StandardScaler"""
    print("\n🔧 ANÁLISIS DEL STANDARDSCALER")
    print("=" * 50)
    
    from sklearn.preprocessing import StandardScaler
    
    # Crear y entrenar scaler
    scaler = StandardScaler()
    target_scaled = scaler.fit_transform(atr_scaled_targets.reshape(-1, 1)).flatten()
    
    print(f"📊 Scaler entrenado:")
    print(f"   Mean: {scaler.mean_[0]:.6f}")
    print(f"   Scale: {scaler.scale_[0]:.6f}")
    print(f"   Var: {scaler.var_[0]:.6f}")
    
    print(f"\n📊 Target normalizado:")
    print(f"   Rango: [{target_scaled.min():.4f}, {target_scaled.max():.4f}]")
    print(f"   Media: {target_scaled.mean():.6f}")
    print(f"   Std: {target_scaled.std():.6f}")
    
    # Probar desnormalización
    print(f"\n🧪 Pruebas de desnormalización:")
    test_values = [0.0, 0.5, -0.5, 1.0, -1.0]
    
    for test_val in test_values:
        denormalized = scaler.inverse_transform([[test_val]])[0, 0]
        print(f"   Normalizado: {test_val:6.2f} → Desnormalizado: {denormalized:8.4f}")
    
    return scaler

def test_prediction_logic(df_with_indicators, scaler):
    """Probar la lógica de predicción paso a paso"""
    print("\n🎯 PRUEBA DE LÓGICA DE PREDICCIÓN")
    print("=" * 50)
    
    # Simular una predicción del modelo (valor normalizado típico)
    simulated_predictions = [0.0, 0.1, -0.1, 0.5, -0.5]
    
    # Usar los últimos datos
    current_price = df_with_indicators['close'].iloc[-1]
    current_atr = df_with_indicators['atr'].iloc[-1]
    
    print(f"📊 Datos actuales:")
    print(f"   Precio actual: ${current_price:,.2f}")
    print(f"   ATR actual: {current_atr:.4f}")
    
    print(f"\n🧮 Simulación de predicciones:")
    
    for pred_normalized in simulated_predictions:
        # Desnormalizar
        atr_scaled_return = scaler.inverse_transform([[pred_normalized]])[0, 0]
        
        # Aplicar fórmula: pred_price = Close_t + y_pred * ATR_14_t
        predicted_price = current_price + (atr_scaled_return * current_atr)
        
        change_pct = ((predicted_price - current_price) / current_price) * 100
        
        print(f"   Pred normalizada: {pred_normalized:6.2f}")
        print(f"   ATR-scaled return: {atr_scaled_return:8.4f}")
        print(f"   Precio predicho: ${predicted_price:,.2f}")
        print(f"   Cambio: {change_pct:+.2f}%")
        print()

def main():
    print("🚨 DIAGNÓSTICO COMPLETO DEL PROBLEMA ATR-SCALER")
    print("=" * 70)
    
    try:
        # 1. Analizar cálculo del target
        df_with_indicators, atr_scaled_targets = analyze_atr_target_calculation()
        
        # 2. Analizar comportamiento del scaler
        scaler = analyze_scaler_behavior(atr_scaled_targets)
        
        # 3. Probar lógica de predicción
        test_prediction_logic(df_with_indicators, scaler)
        
        # 4. Comparar con scaler guardado
        print("\n📁 COMPARACIÓN CON SCALER GUARDADO")
        print("=" * 50)
        
        scalers_path = "models/btc_lstm_model_scalers.pkl"
        if os.path.exists(scalers_path):
            with open(scalers_path, 'rb') as f:
                saved_scalers = pickle.load(f)
            
            print(f"🔑 Claves en archivo: {list(saved_scalers.keys())}")
            
            if 'target_scaler' in saved_scalers:
                saved_scaler = saved_scalers['target_scaler']
                print(f"📊 Scaler guardado:")
                print(f"   Tipo: {type(saved_scaler).__name__}")
                print(f"   Mean: {getattr(saved_scaler, 'mean_', 'N/A')}")
                print(f"   Scale: {getattr(saved_scaler, 'scale_', 'N/A')}")
            else:
                print("❌ No se encontró 'target_scaler' en el archivo")
        else:
            print("❌ Archivo de scalers no encontrado")
        
        print("\n✅ Diagnóstico completado")
        
    except Exception as e:
        print(f"❌ Error en diagnóstico: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
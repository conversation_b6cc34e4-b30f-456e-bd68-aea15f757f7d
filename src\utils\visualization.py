import plotly.graph_objects as go
import plotly.subplots as sp
import plotly.offline as pyo
import pandas as pd
import numpy as np
from datetime import datetime
import os
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.figure import Figure
import base64
from io import BytesIO

class BacktestingVisualizer:
    """Clase para crear visualizaciones del backtesting."""

    def __init__(self):
        self.colors = {
            'price': '#1f77b4',
            'prediction': '#ff7f0e',
            'buy': '#2ca02c',
            'sell': '#d62728',
            'portfolio': '#9467bd'
        }

    def create_backtesting_chart(self, historical_data, predictions_data, trades_data, metrics):
        """
        Crear gráfica completa del backtesting.

        Args:
            historical_data: DataFrame con datos históricos de precios
            predictions_data: Lista de predicciones del modelo
            trades_data: Lista de operaciones ejecutadas
            metrics: Métricas del backtesting
        """
        # Crear subplots
        fig = sp.make_subplots(
            rows=3, cols=1,
            subplot_titles=(
                'Precio BTC vs Predicciones y Señales de Trading',
                'Valor del Portfolio',
                'Volumen de Operaciones'
            ),
            vertical_spacing=0.08,
            row_heights=[0.6, 0.25, 0.15]
        )

        # Preparar datos históricos con corrección de fechas
        df = historical_data.copy()

        # CRÍTICO: Corregir el problema de fechas
        print(f"📊 Debug - Índice original: {df.index[:3] if len(df) > 0 else 'vacío'}")
        print(f"📊 Debug - Tipo de índice: {type(df.index)}")

        # Si el índice no es datetime o tiene fechas incorrectas (1970), reconstruirlo
        if not isinstance(df.index, pd.DatetimeIndex) or (len(df) > 0 and df.index[0].year < 2020):
            print("⚠️ Corrigiendo fechas incorrectas en el índice")
            # Crear un rango de fechas realista basado en datos recientes
            end_date = pd.Timestamp.now()
            start_date = end_date - pd.Timedelta(hours=len(df)-1)
            df.index = pd.date_range(start=start_date, periods=len(df), freq='H')
            print(f"✅ Nuevas fechas: {df.index[0]} a {df.index[-1]}")

        # Si hay columna timestamp, usarla para corregir el índice
        if 'timestamp' in df.columns:
            try:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                # Solo usar la columna timestamp si tiene fechas válidas
                if len(df) > 0 and df['timestamp'].iloc[0].year >= 2020:
                    df = df.set_index('timestamp')
                    print(f"✅ Usando timestamps de columna: {df.index[0]} a {df.index[-1]}")
            except Exception as e:
                print(f"⚠️ Error procesando columna timestamp: {e}")

        print(f"📊 Datos finales: {len(df)} registros desde {df.index[0] if len(df) > 0 else 'N/A'} hasta {df.index[-1] if len(df) > 0 else 'N/A'}")

        # Gráfica principal: Precio vs Predicciones
        self._add_price_and_predictions(fig, df, predictions_data, row=1)

        # Agregar señales de trading
        self._add_trading_signals(fig, df, trades_data, row=1)

        # Gráfica de portfolio
        self._add_portfolio_value(fig, trades_data, metrics, row=2)

        # Gráfica de volumen de operaciones
        self._add_trading_volume(fig, trades_data, row=3)

        # Configurar layout
        self._configure_layout(fig, metrics)

        return fig

    def create_matplotlib_chart(self, historical_data, predictions_data, trades_data, metrics):
        """Crear gráfica usando matplotlib como alternativa."""
        # Preparar datos
        df = historical_data.copy()

        # Validar que tenemos datos
        if df.empty:
            print("⚠️ No hay datos históricos para graficar")
            return self._create_empty_chart(metrics)

        # Asegurar que el índice sea datetime
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.set_index('timestamp')
        elif not isinstance(df.index, pd.DatetimeIndex):
            try:
                df.index = pd.to_datetime(df.index)
            except:
                # Si no se puede convertir, crear un índice datetime artificial
                print("⚠️ Creando índice datetime artificial")
                df.index = pd.date_range(start='2024-01-01', periods=len(df), freq='H')

        # Verificar que tenemos la columna 'close'
        if 'close' not in df.columns:
            print("⚠️ No se encontró columna 'close' en los datos")
            return self._create_empty_chart(metrics)

        # Crear figura con subplots
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
        fig.suptitle(f'Análisis de Backtesting - Retorno: {metrics.get("total_return_pct", 0):.2f}%', fontsize=16)

        # Gráfica principal: Precio vs Predicciones
        ax1.plot(df.index, df['close'], label='Precio Real BTC', color='blue', linewidth=2)

        # Agregar predicciones
        pred_count = 0
        if predictions_data:
            pred_timestamps = []
            pred_prices = []
            for pred in predictions_data:
                try:
                    if isinstance(pred, dict) and 'timestamp' in pred and 'predicted_price' in pred:
                        timestamp = pd.to_datetime(pred['timestamp'])
                        price = float(pred['predicted_price'])
                        pred_timestamps.append(timestamp)
                        pred_prices.append(price)
                        pred_count += 1
                except Exception as e:
                    continue

            if pred_timestamps and pred_prices:
                ax1.scatter(pred_timestamps, pred_prices, label=f'Predicciones LSTM ({pred_count})',
                           color='orange', alpha=0.7, s=30)
                print(f"✅ Graficadas {len(pred_timestamps)} predicciones")
            else:
                print("⚠️ No se pudieron procesar las predicciones para graficar")

        # Agregar señales de trading
        buy_count, sell_count = 0, 0
        if trades_data:
            buy_times, buy_prices = [], []
            sell_times, sell_prices = [], []

            for trade in trades_data:
                try:
                    if isinstance(trade, dict) and 'timestamp' in trade and 'price' in trade:
                        timestamp = pd.to_datetime(trade['timestamp'])
                        price = float(trade['price'])

                        if trade.get('type') == 'BUY':
                            buy_times.append(timestamp)
                            buy_prices.append(price)
                            buy_count += 1
                        elif trade.get('type') == 'SELL':
                            sell_times.append(timestamp)
                            sell_prices.append(price)
                            sell_count += 1
                except Exception as e:
                    continue

            if buy_times:
                ax1.scatter(buy_times, buy_prices, marker='^', color='green',
                           s=100, label=f'Señales de Compra ({buy_count})', zorder=5)
            if sell_times:
                ax1.scatter(sell_times, sell_prices, marker='v', color='red',
                           s=100, label=f'Señales de Venta ({sell_count})', zorder=5)

            print(f"✅ Graficadas {buy_count} compras y {sell_count} ventas")

        ax1.set_title('Precio BTC vs Predicciones y Señales de Trading')
        ax1.set_ylabel('Precio USD')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Gráfica de portfolio
        self._create_portfolio_chart(ax2, df, trades_data, metrics)

        # Formatear fechas en el eje x
        for ax in [ax1, ax2]:
            try:
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
                ax.xaxis.set_major_locator(mdates.HourLocator(interval=max(1, len(df)//10)))
                plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
            except Exception as e:
                print(f"⚠️ Error formateando fechas: {e}")

        plt.tight_layout()
        return fig

    def _create_portfolio_chart(self, ax, df, trades_data, metrics):
        """Crear gráfica del valor del portfolio."""
        initial_balance = metrics.get('initial_balance', 10000)

        # Si tenemos datos de trades, usar esos para calcular el portfolio
        if trades_data:
            portfolio_times = []
            portfolio_values = []
            current_balance = initial_balance
            btc_balance = 0

            # Procesar cada trade
            for trade in trades_data:
                try:
                    timestamp = pd.to_datetime(trade['timestamp'])
                    price = float(trade['price'])
                    amount = float(trade.get('amount', 0))

                    if trade.get('type') == 'BUY':
                        current_balance -= (price * amount)
                        btc_balance += amount
                    elif trade.get('type') == 'SELL':
                        current_balance += (price * amount)
                        btc_balance -= amount

                    # Calcular valor total del portfolio
                    portfolio_value = current_balance + (btc_balance * price)
                    portfolio_times.append(timestamp)
                    portfolio_values.append(portfolio_value)

                except Exception:
                    continue

            if portfolio_times and portfolio_values:
                ax.plot(portfolio_times, portfolio_values, label='Valor del Portfolio',
                       color='purple', linewidth=2)
            else:
                # Si no hay trades válidos, mostrar línea plana
                ax.axhline(y=initial_balance, color='purple', linestyle='--',
                          label=f'Balance Inicial: ${initial_balance:,.2f}')
        else:
            # Sin trades, mostrar línea plana del balance inicial
            ax.axhline(y=initial_balance, color='purple', linestyle='--',
                      label=f'Balance Inicial: ${initial_balance:,.2f}')

        ax.set_title('Valor del Portfolio')
        ax.set_ylabel('Valor USD')
        ax.set_xlabel('Fecha')
        ax.legend()
        ax.grid(True, alpha=0.3)

    def _create_empty_chart(self, metrics):
        """Crear una gráfica vacía cuando no hay datos."""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
        fig.suptitle(f'Análisis de Backtesting - Sin Datos Disponibles', fontsize=16)

        # Gráfica vacía con mensaje
        ax1.text(0.5, 0.5, 'No hay datos históricos disponibles',
                ha='center', va='center', transform=ax1.transAxes, fontsize=14)
        ax1.set_title('Precio BTC vs Predicciones y Señales de Trading')
        ax1.set_ylabel('Precio USD')

        # Portfolio vacío
        initial_balance = metrics.get('initial_balance', 10000)
        ax2.axhline(y=initial_balance, color='purple', linestyle='--',
                   label=f'Balance Inicial: ${initial_balance:,.2f}')
        ax2.set_title('Valor del Portfolio')
        ax2.set_ylabel('Valor USD')
        ax2.legend()

        plt.tight_layout()
        return fig

    def _add_price_and_predictions(self, fig, df, predictions_data, row):
        """Agregar líneas de precio real y predicciones."""
        # Precio real
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df['close'],
                mode='lines',
                name='Precio Real BTC',
                line=dict(color=self.colors['price'], width=2),
                hovertemplate='<b>Precio Real</b><br>' +
                             'Fecha: %{x}<br>' +
                             'Precio: $%{y:,.2f}<br>' +
                             '<extra></extra>'
            ),
            row=row, col=1
        )

        # Predicciones
        if predictions_data:
            pred_timestamps = []
            pred_prices = []

            for i, pred in enumerate(predictions_data):
                if 'timestamp' in pred and 'predicted_price' in pred:
                    timestamp = pd.to_datetime(pred['timestamp'])
                    # Si el timestamp es incorrecto (1970), usar el índice del DataFrame
                    if timestamp.year < 2020 and i < len(df):
                        timestamp = df.index[min(i + 60, len(df) - 1)]  # Offset para lookback window
                    pred_timestamps.append(timestamp)
                    pred_prices.append(pred['predicted_price'])

            if pred_timestamps and pred_prices:
                fig.add_trace(
                    go.Scatter(
                        x=pred_timestamps,
                        y=pred_prices,
                        mode='markers+lines',
                        name='Predicciones LSTM',
                        line=dict(color=self.colors['prediction'], width=2, dash='dot'),
                        marker=dict(size=4, color=self.colors['prediction']),
                        hovertemplate='<b>Predicción LSTM</b><br>' +
                                     'Fecha: %{x}<br>' +
                                     'Precio Predicho: $%{y:,.2f}<br>' +
                                     '<extra></extra>'
                    ),
                    row=row, col=1
                )

    def _add_trading_signals(self, fig, df, trades_data, row):
        """Agregar marcadores de compra y venta."""
        if not trades_data:
            return

        buy_times, buy_prices, buy_amounts = [], [], []
        sell_times, sell_prices, sell_amounts = [], [], []

        for i, trade in enumerate(trades_data):
            timestamp = pd.to_datetime(trade['timestamp'])
            # Si el timestamp es incorrecto (1970), usar fechas del DataFrame
            if timestamp.year < 2020 and i < len(df):
                # Buscar una fecha apropiada basada en el precio
                price_match_idx = None
                trade_price = trade['price']
                for j, row_price in enumerate(df['close']):
                    if abs(row_price - trade_price) < trade_price * 0.01:  # 1% tolerance
                        price_match_idx = j
                        break
                if price_match_idx is not None:
                    timestamp = df.index[price_match_idx]
                else:
                    # Fallback: usar posición relativa
                    timestamp = df.index[min(i + 60, len(df) - 1)]

            price = trade['price']
            amount = trade['amount']

            if trade['type'] == 'BUY':
                buy_times.append(timestamp)
                buy_prices.append(price)
                buy_amounts.append(amount)
            elif trade['type'] == 'SELL':
                sell_times.append(timestamp)
                sell_prices.append(price)
                sell_amounts.append(amount)

        # Marcadores de compra
        if buy_times:
            fig.add_trace(
                go.Scatter(
                    x=buy_times,
                    y=buy_prices,
                    mode='markers',
                    name='Señales de Compra',
                    marker=dict(
                        symbol='triangle-up',
                        size=12,
                        color=self.colors['buy'],
                        line=dict(width=2, color='white')
                    ),
                    hovertemplate='<b>COMPRA</b><br>' +
                                 'Fecha: %{x}<br>' +
                                 'Precio: $%{y:,.2f}<br>' +
                                 'Cantidad: %{customdata:.6f} BTC<br>' +
                                 '<extra></extra>',
                    customdata=buy_amounts
                ),
                row=row, col=1
            )

        # Marcadores de venta
        if sell_times:
            fig.add_trace(
                go.Scatter(
                    x=sell_times,
                    y=sell_prices,
                    mode='markers',
                    name='Señales de Venta',
                    marker=dict(
                        symbol='triangle-down',
                        size=12,
                        color=self.colors['sell'],
                        line=dict(width=2, color='white')
                    ),
                    hovertemplate='<b>VENTA</b><br>' +
                                 'Fecha: %{x}<br>' +
                                 'Precio: $%{y:,.2f}<br>' +
                                 'Cantidad: %{customdata:.6f} BTC<br>' +
                                 '<extra></extra>',
                    customdata=sell_amounts
                ),
                row=row, col=1
            )

    def _add_portfolio_value(self, fig, trades_data, metrics, row):
        """Agregar gráfica del valor del portfolio."""
        if not trades_data:
            return

        # Calcular valor del portfolio a lo largo del tiempo
        portfolio_times = []
        portfolio_values = []
        current_balance = metrics.get('initial_balance', 10000)
        btc_balance = 0

        # Ordenar trades por timestamp
        sorted_trades = sorted(trades_data, key=lambda x: x['timestamp'])

        for trade in sorted_trades:
            timestamp = pd.to_datetime(trade['timestamp'])
            price = trade['price']

            if trade['type'] == 'BUY':
                # Calcular cuánto podemos comprar (máximo 80% del balance)
                amount_to_invest = min(current_balance * 0.8, current_balance)
                btc_bought = amount_to_invest / price if price > 0 else 0
                current_balance -= amount_to_invest
                btc_balance += btc_bought
            elif trade['type'] == 'SELL':
                # Vender todo el BTC
                usd_received = btc_balance * price
                current_balance += usd_received
                btc_balance = 0

            # Calcular valor total del portfolio
            portfolio_value = current_balance + (btc_balance * price)
            portfolio_times.append(timestamp)
            portfolio_values.append(portfolio_value)

        if portfolio_times and portfolio_values:
            fig.add_trace(
                go.Scatter(
                    x=portfolio_times,
                    y=portfolio_values,
                    mode='lines+markers',
                    name='Valor del Portfolio',
                    line=dict(color=self.colors['portfolio'], width=2),
                    hovertemplate='<b>Valor Portfolio</b><br>' +
                                'Fecha: %{x}<br>' +
                                'Valor: $%{y:,.2f}<br>' +
                                '<extra></extra>',
                    fill='tonexty'
                ),
                row=row, col=1
            )

            # Línea de balance inicial
            initial_balance = metrics.get('initial_balance', 10000)
            fig.add_hline(
                y=initial_balance,
                line_dash="dash",
                line_color="gray",
                annotation_text=f"Balance Inicial: ${initial_balance:,.2f}",
                row=row, col=1
            )

    def _add_trading_volume(self, fig, trades_data, row):
        """Agregar gráfica de volumen de operaciones."""
        if not trades_data:
            return

        # Agrupar operaciones por día
        daily_trades = {}
        for trade in trades_data:
            date = pd.to_datetime(trade['timestamp']).date()
            if date not in daily_trades:
                daily_trades[date] = {'buy': 0, 'sell': 0}

            if trade['type'] == 'BUY':
                daily_trades[date]['buy'] += trade['amount'] * trade['price']
            elif trade['type'] == 'SELL':
                daily_trades[date]['sell'] += trade['amount'] * trade['price']

        dates = list(daily_trades.keys())
        buy_volumes = [daily_trades[date]['buy'] for date in dates]
        sell_volumes = [daily_trades[date]['sell'] for date in dates]

        # Volumen de compras
        fig.add_trace(
            go.Bar(
                x=dates,
                y=buy_volumes,
                name='Volumen Compras',
                marker_color=self.colors['buy'],
                opacity=0.7
            ),
            row=row, col=1
        )

        # Volumen de ventas
        fig.add_trace(
            go.Bar(
                x=dates,
                y=sell_volumes,
                name='Volumen Ventas',
                marker_color=self.colors['sell'],
                opacity=0.7
            ),
            row=row, col=1
        )

    def _configure_layout(self, fig, metrics):
        """Configurar el layout de la gráfica."""
        total_return = metrics.get('total_return_pct', 0)
        total_trades = metrics.get('total_trades', 0)
        win_rate = metrics.get('win_rate', 0)

        fig.update_layout(
            title={
                'text': f'<b>Análisis de Backtesting - Retorno: {total_return:.2f}% | Operaciones: {total_trades} | Win Rate: {win_rate:.1f}%</b>',
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 16}
            },
            height=1000,  # Aumentar altura para mejor visualización
            width=1400,   # Ancho fijo para consistencia
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1,
                bgcolor='rgba(255,255,255,0.8)'
            ),
            hovermode='x unified',
            template='plotly_white',  # Usar un tema limpio
            margin=dict(l=50, r=50, t=100, b=100),
            plot_bgcolor='rgba(240,240,240,0.9)'
        )

        # Configuración de fuentes
        font_dict = dict(family="Arial, sans-serif", size=12, color="#333")

        # Configurar ejes para cada subplot
        fig.update_xaxes(
            title_text="Fecha y Hora",
            title_font=font_dict,
            showline=True,
            linewidth=1,
            linecolor='#ddd',
            mirror=True,
            showgrid=True,
            gridwidth=0.5,
            gridcolor='#f0f0f0',
            row=3,
            col=1
        )

        # Configuración de ejes Y para cada subplot
        yaxis_config = {
            'showline': True,
            'linewidth': 1,
            'linecolor': '#ddd',
            'mirror': True,
            'showgrid': True,
            'gridwidth': 0.5,
            'gridcolor': '#f0f0f0',
            'zeroline': False,
            'title_font': font_dict,
            'tickfont': font_dict
        }

        fig.update_yaxes(
            title_text="Precio BTC (USD)",
            **yaxis_config,
            row=1,
            col=1
        )

        fig.update_yaxes(
            title_text="Valor del Portfolio (USD)",
            **yaxis_config,
            row=2,
            col=1
        )

        fig.update_yaxes(
            title_text="Volumen de Operaciones (USD)",
            **yaxis_config,
            row=3,
            col=1,
            tickformat=",.0f"  # Formato de números enteros para el volumen
        )

        # Ajustar márgenes entre subplots
        fig.update_layout(
            margin=dict(l=50, r=50, t=100, b=100),
            hoverlabel=dict(
                bgcolor="white",
                font_size=12,
                font_family="Arial"
            )
        )

    def save_chart(self, fig, filename=None, historical_data=None, predictions_data=None, trades_data=None, metrics=None):
        """Guardar la gráfica como archivo HTML."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"backtesting_chart_{timestamp}.html"

        # Crear directorio si no existe
        charts_dir = "charts"
        os.makedirs(charts_dir, exist_ok=True)
        filepath = os.path.join(charts_dir, filename)

        try:
            # Primero intentar con Plotly
            if fig is not None:
                # Configurar el diseño del gráfico
                self._configure_layout(fig, metrics or {})

                # Guardar el gráfico
                fig.write_html(
                    filepath,
                    full_html=True,
                    include_plotlyjs='cdn',
                    config={'displayModeBar': True}
                )
                print(f"Gráfico guardado exitosamente en: {filepath}")
                return filepath
        except Exception as e:
            print(f"Error al guardar con Plotly: {e}")
            print("Intentando con matplotlib como alternativa...")

        # Si falla Plotly, usar matplotlib
        return self.save_matplotlib_chart(
            filepath,
            historical_data,
            predictions_data,
            trades_data,
            metrics
        )

    def save_matplotlib_chart(self, filepath, historical_data=None, predictions_data=None, trades_data=None, metrics=None):
        """Guardar gráfica usando matplotlib."""
        # Si no tenemos los datos, usar datos por defecto para testing
        if historical_data is None or historical_data.empty:
            print("⚠️ Creando datos de prueba para la gráfica")
            # Crear datos de prueba
            dates = pd.date_range(start='2024-01-01', periods=100, freq='H')
            historical_data = pd.DataFrame({
                'close': np.random.randn(100).cumsum() + 50000
            }, index=dates)
            predictions_data = []
            trades_data = []
            metrics = {'total_return_pct': 0.5, 'initial_balance': 10000}

        print(f"📊 Creando gráfica matplotlib con {len(historical_data)} datos históricos")

        # Crear gráfica matplotlib
        fig = self.create_matplotlib_chart(historical_data, predictions_data, trades_data, metrics)

        # Asegurar que el directorio existe
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        # Guardar como PNG y crear HTML que lo contenga
        png_path = filepath.replace('.html', '.png')
        fig.savefig(png_path, dpi=300, bbox_inches='tight', facecolor='white')

        # Crear HTML que muestre la imagen
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Backtesting Chart</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .chart-container {{ text-align: center; }}
        img {{ max-width: 100%; height: auto; }}
    </style>
</head>
<body>
    <div class="chart-container">
        <h1>Análisis de Backtesting - Modelo LSTM</h1>
        <img src="{os.path.basename(png_path)}" alt="Backtesting Chart">
        <p>Gráfica generada con matplotlib como alternativa a Plotly</p>
    </div>
</body>
</html>
        """

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)

        plt.close(fig)  # Liberar memoria
        print(f"✅ Gráfica matplotlib guardada en: {filepath}")
        return filepath

    def create_backtesting_chart_with_fallback(self, historical_data, predictions_data, trades_data, metrics):
        """Crear gráfica con fallback a matplotlib si Plotly falla."""
        try:
            # Intentar con Plotly primero
            return self.create_backtesting_chart(historical_data, predictions_data, trades_data, metrics)
        except Exception as e:
            print(f"Error con Plotly: {e}. Usando matplotlib...")
            # Usar matplotlib como alternativa
            return self.create_matplotlib_chart(historical_data, predictions_data, trades_data, metrics)

    def show_chart(self, fig):
        """Mostrar la gráfica en el navegador."""
        if hasattr(fig, 'show'):  # Plotly figure
            fig.show()
        else:  # Matplotlib figure
            plt.show()
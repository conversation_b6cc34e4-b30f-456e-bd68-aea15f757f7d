"""
Configuración principal del Dashboard BTC
"""
import os
from pathlib import Path

# Directorio base del proyecto
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# Configuración de Binance
BINANCE_CONFIG = {
    "symbol": "BTCUSDT",
    "interval": "15m",
    "limit": 200  # Suficientes datos para predicciones completas (50 horas)
}

# Configuración del servidor
SERVER_CONFIG = {
    "host": "0.0.0.0",
    "port": 8050,
    "debug": True,
    "callback_timeout": 60,  # Timeout para callbacks en segundos
    "request_timeout": 30   # Timeout para requests HTTP
}

# Configuración de actualización
UPDATE_CONFIG = {
    "interval_ms": 30 * 1000,  # 30 segundos para testing (antes 15 minutos)
    "max_retries": 3,  # Máximo reintentos para datos
    "timeout_seconds": 30  # Timeout para peticiones
}

# Configuración de la gráfica
CHART_CONFIG = {
    "height": 500,
    "template": "plotly_white",
    "recent_data_rows": 5
}

# Configuración de colores
COLORS = {
    "primary": "#17a2b8",
    "success": "#28a745",
    "info": "#17a2b8",
    "warning": "#ffc107",
    "danger": "#dc3545",
    "candlestick_green": "#26a69a",
    "candlestick_red": "#ef5350"
}

# Configuración de IA y Predicciones
AI_CONFIG = {
    "model_path": "models/btc_lstm_model.h5",
    "confidence_threshold": 0.75,
    "prediction_horizon": 1,  # Períodos hacia adelante
    "min_data_points": 30,  # Mínimo de datos para predicción
    "ideal_data_points": 200,  # Ideal de datos para predicción
    "sequence_length": 30,  # Longitud de secuencia LSTM (reducido de 60 para mejor precisión)
    "retrain_threshold": 0.3,  # Umbral de error para reentrenamiento
    "max_prediction_age_minutes": 30  # Máxima edad de predicción en minutos
}

# Configuración de indicadores técnicos
TECHNICAL_INDICATORS_CONFIG = {
    "rsi_period": 14,
    "macd_fast": 12,
    "macd_slow": 26,
    "macd_signal": 9,
    "bb_period": 20,
    "bb_std": 2,
    "sma_periods": [20, 50],
    "ema_periods": [12, 26],
    "atr_period": 14,
    "adx_period": 14
}

# Configuración de logging
# Actualizar LOGGING_CONFIG (líneas 75-82)
LOGGING_CONFIG = {
    "level": "DEBUG",  # Cambiado de INFO a DEBUG para más detalle
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s",
    "file": BASE_DIR / "logs" / "dashboard.log",
    "prediction_file": BASE_DIR / "logs" / "predictions.log",  # Nuevo archivo específico
    "max_file_size": 10 * 1024 * 1024,  # 10MB
    "backup_count": 10  # Más backups para análisis histórico
}



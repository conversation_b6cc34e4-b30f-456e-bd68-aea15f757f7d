import logging
import os
from pathlib import Path
from datetime import datetime
import time

import dash
from dash import dcc, html, Input, Output, callback
import dash_bootstrap_components as dbc
import pandas as pd
import numpy as np

from config.settings import SERVER_CONFIG, UPDATE_CONFIG, LOGGING_CONFIG
from services.binance_service import BinanceService
from services.data_service import DataService
from services.chart_service import ChartService
from ai.adaptive_system import AdaptiveLSTMSystem
from utils.logging_setup import setup_logging

# Configuración del logging
setup_logging(LOGGING_CONFIG)
logger = logging.getLogger(__name__)

class DashboardApp:
    """Dashboard principal para visualización de precios y predicciones de BTC."""

    def __init__(self):
        self.app = self._create_app()
        self.binance_service = BinanceService()
        self.data_service = DataService()
        self.ai_system = AdaptiveLSTMSystem()
        self.chart_service = ChartService()  # Removido el parámetro self.ai_system
        
        self.callback_lock = False
        self.last_callback_time = 0
        self.historical_periods = 100
        
        self._setup_layout()
        self._setup_callbacks()

    def _create_app(self) -> dash.Dash:
        """Crea la instancia de la aplicación Dash con temas personalizados."""
        return dash.Dash(
            __name__,
            external_stylesheets=[
                dbc.themes.BOOTSTRAP,
                "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
            ],
            meta_tags=[{
                'name': 'viewport',
                'content': 'width=device-width, initial-scale=1.0'
            }]
        )

    def _get_custom_html(self) -> str:
        """Retorna el HTML personalizado para el head."""
        return """
        <style>
            .card-header-custom {
                background-color: #343a40;
                color: white;
                font-weight: bold;
            }
            .card-body-custom {
                background-color: #f8f9fa;
            }
            .status-dot {
                height: 15px;
                width: 15px;
                border-radius: 50%;
                display: inline-block;
                margin-right: 8px;
                vertical-align: middle;
            }
            .status-ok { background-color: #28a745; }
            .status-warning { background-color: #ffc107; }
            .status-error { background-color: #dc3545; }
            .text-success { color: #28a745 !important; }
            .text-danger { color: #dc3545 !important; }
            .text-warning { color: #ffc107 !important; }
            .fw-bold { font-weight: bold; }
            .small { font-size: 0.875em; }
            .text-muted { color: #6c757d !important; }
            .me-2 { margin-right: 0.5rem !important; }
            .d-flex { display: flex !important; }
            .align-items-center { align-items: center !important; }
        </style>
        """

    def _setup_layout(self):
        """Configura el layout principal del dashboard."""
        self.app.title = "FinfinLSTM BTC Dashboard"
        self.app.index_string = f"""
        <!DOCTYPE html>
        <html>
            <head>
                {{%metas%}}
                <title>{{%title%}}</title>
                {{%favicon%}}
                {{%css%}}
                {self._get_custom_html()}
            </head>
            <body>
                {{%app_entry%}}
                <footer>
                    {{%config%}}
                    {{%scripts%}}
                    {{%renderer%}}
                </footer>
            </body>
        </html>
        """
        self.app.layout = dbc.Container(fluid=True, children=[
            dcc.Interval(id='interval-component', interval=UPDATE_CONFIG['interval_ms'], n_intervals=0),
            dbc.Row([
                dbc.Col(html.H1("FinfinLSTM BTC Dashboard", className="text-white"), width=8),
                dbc.Col(html.Div(id='last-update', className="text-white text-end small mt-3"), width=4)
            ], className="bg-dark p-3 align-items-center"),
            
            dbc.Row([
                dbc.Col(md=8, children=[
                    dbc.Card([
                        dbc.CardHeader(
                            dbc.Row([
                                dbc.Col("Gráfico de Precios de BTC/USDT", width=6),
                                dbc.Col(id='chart-type-indicator', className="text-end small", width=6)
                            ]),
                            className="card-header-custom"
                        ),
                        dbc.CardBody(dcc.Graph(id='price-chart'), className="p-0")
                    ])
                ]),
                dbc.Col(md=4, children=[
                    dbc.Card([
                        dbc.CardHeader("Controles", className="card-header-custom"),
                        dbc.CardBody([
                            dbc.ButtonGroup([
                                dbc.Button("Línea", id="line-chart-btn", n_clicks=0, color="primary"),
                                dbc.Button("Velas", id="candlestick-btn", n_clicks=0, color="secondary"),
                            ]),
                            html.Hr(),
                            html.Label("Períodos Históricos:"),
                            dcc.Slider(id='historical-size-selector', min=10, max=200, step=10, value=100,
                                       marks={i: str(i) for i in range(10, 201, 30)})
                        ])
                    ]),
                    dbc.Card([
                        dbc.CardHeader("Métricas en Tiempo Real", className="card-header-custom"),
                        dbc.CardBody([
                            html.Div([html.Strong("Precio Actual: "), html.Span(id='current-price')]),
                            html.Div([html.Strong("Volumen 24h: "), html.Span(id='volume-24h')]),
                            html.Div([html.Strong("Datos Recientes: "), html.Span(id='recent-data')]),
                        ])
                    ], className="mt-3")
                ])
            ]),
            
            dbc.Row([
                dbc.Col(md=3, children=dbc.Card([
                    dbc.CardHeader("Estado del Sistema de IA", className="card-header-custom"),
                    dbc.CardBody([
                        html.Div(id='ai-status', className="d-flex align-items-center"),
                        html.P(id='ai-status-text', className="small text-muted mt-2")
                    ])
                ])),
                dbc.Col(md=3, children=dbc.Card([
                    dbc.CardHeader("Predicción de Precio", className="card-header-custom"),
                    dbc.CardBody([
                        html.Div([
                            html.Small("Precio Actual:"),
                            html.H6(id='current-price-display', className="fw-bold text-primary mb-2")
                        ]),
                        html.Div([
                            html.Small("Predicción:"),
                            html.H4(id='predicted-price', className="fw-bold")
                        ]),
                        html.Div(id='prediction-confidence', className="small"),
                        html.Div([
                            html.Small("Cambio esperado: "),
                            html.Span(id='price-change-pct', className="fw-bold")
                        ]),
                        html.Div(id='prediction-indicator', className="mt-2")
                    ])
                ])),
            ], className="mt-3")
        ])

    def _setup_callbacks(self):
        """Configura los callbacks de la aplicación Dash."""
        @self.app.callback(
            [Output('price-chart', 'figure'),
             Output('current-price', 'children'),
             Output('volume-24h', 'children'),
             Output('last-update', 'children'),
             Output('recent-data', 'children'),
             Output('chart-type-indicator', 'children'),
             Output('ai-status', 'children'),
             Output('ai-status-text', 'children'),
             Output('predicted-price', 'children'),
             Output('prediction-confidence', 'children'),
             Output('current-price-display', 'children'),
             Output('price-change-pct', 'children'),
             Output('prediction-indicator', 'children')],
            [Input('interval-component', 'n_intervals'),
             Input('line-chart-btn', 'n_clicks'),
             Input('candlestick-btn', 'n_clicks'),
             Input('historical-size-selector', 'value')]
        )
        def update_chart(n, line_clicks, candlestick_clicks, historical_size=100):
            try:
                current_time = time.time()
                if self.callback_lock or (self.last_callback_time and (current_time - self.last_callback_time) < 5):
                    logger.debug("Callback en ejecución o demasiado frecuente, saltando...")
                    return [dash.no_update] * 13

                self.callback_lock = True
                self.last_callback_time = current_time
                logger.debug(f"Iniciando callback #{n} en tiempo {current_time:.1f}")

                df = self.binance_service.get_klines_data()
                if df.empty:
                    logger.warning("No se pudieron obtener datos de Binance")
                    return (self.chart_service._create_error_chart(), "Error", "Error", "Error", "Error obteniendo datos", "Tipo: Error", "ERROR", "Error en sistema de IA", "Error", "Error", "Error", "Error", "Error")

                chart_type = self._determine_chart_type(line_clicks, candlestick_clicks)
                metrics = self.data_service.process_market_data(df)
                
                ai_status, ai_status_text, predicted_price, prediction_confidence = self._analyze_with_ai(df)
                
                prediction_data = None
                if ai_status != "ERROR" and predicted_price != "No disponible":
                    try:
                        price_str = predicted_price.replace('$', '').replace(',', '')
                        price_value = float(price_str)
                        confidence_str = prediction_confidence.replace('Confianza: ', '').replace('%', '')
                        confidence_value = float(confidence_str) / 100
                        current_price_value = df['close'].iloc[-1]
                        
                        prediction_data = {
                            'price': price_value,
                            'confidence': confidence_value,
                            'current_price': current_price_value
                        }
                    except (ValueError, IndexError) as e:
                        logger.error(f"Error procesando datos de predicción: {e}")
                        prediction_data = None

                figure = self.chart_service.create_price_chart(df, chart_type, prediction_data)
                
                current_price = metrics.get('current_price', 0) if metrics else 0
                volume_24h = metrics.get('volume_24h', 0) if metrics else 0
                last_update = metrics.get('last_update', 'No disponible') if metrics else 'No disponible'
                recent_data = metrics.get('recent_data', 'Sin datos') if metrics else 'Sin datos'
                
                prediction_indicator = self.chart_service.create_prediction_indicator(
                    prediction_data['price'] if prediction_data else 0,
                    current_price
                )
                
                price_change_pct = "0.0%"
                if prediction_data and prediction_data.get('price') and prediction_data.get('current_price'):
                    predicted = prediction_data['price']
                    current = prediction_data['current_price']
                    change_pct = ((predicted - current) / current) * 100
                    color_class = "text-success" if change_pct > 0 else "text-danger" if change_pct < 0 else "text-muted"
                    price_change_pct = html.Span(f"{change_pct:+.2f}%", className=color_class)

                return (
                    figure,
                    current_price,
                    volume_24h,
                    last_update,
                    recent_data,
                    f"Tipo: {chart_type.capitalize()}",
                    ai_status,
                    ai_status_text,
                    predicted_price,
                    prediction_confidence,
                    f"${current_price:.2f}",
                    price_change_pct,
                    prediction_indicator
                )

            except Exception as e:
                logger.error(f"Error en callback de actualización: {e}", exc_info=True)
                return [dash.no_update] * 13
            finally:
                self.callback_lock = False

    def _determine_chart_type(self, line_clicks, candlestick_clicks) -> str:
        """Determina el tipo de gráfico a mostrar basado en los clicks de los botones."""
        ctx = dash.callback_context
        if not ctx.triggered:
            return 'line'
        button_id = ctx.triggered[0]['prop_id'].split('.')[0]
        if button_id == 'candlestick-btn':
            return 'candlestick'
        return 'line'

    def _analyze_with_ai(self, df: pd.DataFrame) -> tuple:
        """Ejecuta el sistema de IA para obtener predicciones y análisis."""
        try:
            prediction = self.ai_system.adaptive_prediction(df)
            if not prediction or 'price' not in prediction or pd.isna(prediction['price']):
                raise ValueError("Predicción inválida o nula recibida del sistema de IA")

            price = prediction['price']
            confidence = prediction['confidence']
            
            accuracy = prediction.get('accuracy', 0)
            accuracy_pct = accuracy * 100 if accuracy else 0
            status_text = f"Modelo: {prediction.get('model_type', 'N/A')}, Precisión: {accuracy_pct:.2f}%"
            status_html = html.Div([html.Span(className="status-dot status-ok"), "Operacional"])
            
            predicted_price_str = f"${price:,.2f}"
            confidence_pct = confidence * 100
            confidence_str = f"Confianza: {confidence_pct:.1f}%"
            
            current_price = df['close'].iloc[-1]

            return status_html, status_text, predicted_price_str, confidence_str

        except Exception as e:
            logger.error(f"Error en _analyze_with_ai: {e}", exc_info=True)
            status_html = html.Div([html.Span(className="status-dot status-error"), "Error"])
            return status_html, f"Error en IA: {e}", "No disponible", "Confianza: 0%" 



    

    


    def run(self):
        """Ejecuta el servidor del dashboard."""
        try:
            logger.info(f"Iniciando Dashboard BTC en http://{SERVER_CONFIG['host']}:{SERVER_CONFIG['port']}")
            self.app.run_server(
                debug=SERVER_CONFIG['debug'],
                host=SERVER_CONFIG['host'],
                port=SERVER_CONFIG['port'],
                dev_tools_hot_reload=False,
                threaded=True
            )
        except Exception as e:
            logger.critical(f"Error fatal al ejecutar el dashboard: {e}", exc_info=True)
            raise

def main():
    """Función principal para instanciar y ejecutar el dashboard."""
    try:
        dashboard = DashboardApp()
        dashboard.run()
    except KeyboardInterrupt:
        logger.info("Dashboard detenido por el usuario.")
    except Exception as e:
        logger.error(f"Error fatal en main: {e}", exc_info=True)

if __name__ == '__main__':
    main()
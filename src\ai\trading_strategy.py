import logging
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime

class SimplePredictionStrategy:
    """
    Estrategia de trading simple basada únicamente en predicciones del modelo LSTM.
    No usa indicadores técnicos, solo la dirección y magnitud de la predicción.
    """
    
    def __init__(self, 
                 min_prediction_confidence: float = 0.6,
                 min_price_change_threshold: float = 0.005,  # 0.5%
                 max_position_size: float = 1000.0,  # USD
                 stop_loss_percentage: float = 0.02,  # 2%
                 take_profit_percentage: float = 0.015):  # 1.5%
        """
        Inicializa la estrategia de trading.
        
        Args:
            min_prediction_confidence: Confianza mínima requerida para abrir posición
            min_price_change_threshold: Cambio mínimo de precio para considerar señal
            max_position_size: Tamaño máximo de posición en USD
            stop_loss_percentage: Porcentaje de stop loss
            take_profit_percentage: Porcentaje de take profit
        """
        self.min_prediction_confidence = min_prediction_confidence
        self.min_price_change_threshold = min_price_change_threshold
        self.max_position_size = max_position_size
        self.stop_loss_percentage = stop_loss_percentage
        self.take_profit_percentage = take_profit_percentage
        
        # Estado de la posición actual
        self.current_position = None
        self.position_entry_price = None
        self.position_size = 0.0
        self.position_type = None  # 'long' o 'short'
        
        self.logger = logging.getLogger(__name__)
        
    def generate_signal(self, 
                       current_price: float, 
                       predicted_price: float, 
                       prediction_confidence: float) -> Dict:
        """
        Genera señal de trading basada en la predicción del modelo.
        
        Args:
            current_price: Precio actual del activo
            predicted_price: Precio predicho por el modelo
            prediction_confidence: Confianza de la predicción (0-1)
            
        Returns:
            Dict con la señal de trading
        """
        try:
            # Validar que los precios sean válidos
            if current_price <= 0:
                return {
                    'timestamp': datetime.now(),
                    'action': 'error',
                    'reason': 'Precio actual inválido (cero o negativo)'
                }
            
            if predicted_price <= 0 or np.isnan(predicted_price) or np.isinf(predicted_price):
                return {
                    'timestamp': datetime.now(),
                    'action': 'error',
                    'reason': 'Precio predicho inválido'
                }
            
            if prediction_confidence < 0 or prediction_confidence > 1 or np.isnan(prediction_confidence):
                return {
                    'timestamp': datetime.now(),
                    'action': 'error',
                    'reason': 'Confianza de predicción inválida'
                }
            
            # Calcular cambio porcentual predicho
            price_change_pct = (predicted_price - current_price) / current_price
            
            signal = {
                'timestamp': datetime.now(),
                'current_price': current_price,
                'predicted_price': predicted_price,
                'price_change_pct': price_change_pct,
                'prediction_confidence': prediction_confidence,
                'action': 'hold',
                'position_size': 0.0,
                'stop_loss': None,
                'take_profit': None,
                'reason': ''
            }
            
            # Verificar si hay posición abierta
            if self.current_position:
                exit_signal = self._check_exit_conditions(current_price)
                if exit_signal:
                    return exit_signal
            
            # Verificar condiciones para nueva posición
            if not self.current_position:
                entry_signal = self._check_entry_conditions(
                    current_price, predicted_price, 
                    price_change_pct, prediction_confidence
                )
                if entry_signal:
                    return entry_signal
            
            signal['reason'] = 'No hay condiciones para trading'
            return signal
            
        except Exception as e:
            self.logger.error(f"Error generando señal: {e}")
            return {
                'timestamp': datetime.now(),
                'action': 'error',
                'reason': f'Error: {str(e)}'
            }
    
    def _check_entry_conditions(self, 
                               current_price: float, 
                               predicted_price: float,
                               price_change_pct: float, 
                               prediction_confidence: float) -> Optional[Dict]:
        """
        Verifica condiciones para abrir nueva posición.
        """
        # Verificar confianza mínima
        if prediction_confidence < self.min_prediction_confidence:
            return None
            
        # Verificar cambio mínimo de precio
        if abs(price_change_pct) < self.min_price_change_threshold:
            return None
        
        # Determinar tipo de posición
        if price_change_pct > 0:
            # Predicción alcista -> Posición larga
            action = 'buy'
            position_type = 'long'
            stop_loss = current_price * (1 - self.stop_loss_percentage)
            take_profit = current_price * (1 + self.take_profit_percentage)
        else:
            # Predicción bajista -> Posición corta
            action = 'sell'
            position_type = 'short'
            stop_loss = current_price * (1 + self.stop_loss_percentage)
            take_profit = current_price * (1 - self.take_profit_percentage)
        
        # Calcular tamaño de posición basado en confianza
        position_size = min(
            self.max_position_size * prediction_confidence,
            self.max_position_size
        )
        
        # Actualizar estado interno
        self.current_position = {
            'type': position_type,
            'entry_price': current_price,
            'size': position_size,
            'timestamp': datetime.now()
        }
        self.position_entry_price = current_price
        self.position_size = position_size
        self.position_type = position_type
        
        return {
            'timestamp': datetime.now(),
            'current_price': current_price,
            'predicted_price': predicted_price,
            'price_change_pct': price_change_pct,
            'prediction_confidence': prediction_confidence,
            'action': action,
            'position_size': position_size,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'reason': f'Predicción {"alcista" if price_change_pct > 0 else "bajista"} con {prediction_confidence:.1%} confianza'
        }
    
    def _check_exit_conditions(self, current_price: float) -> Optional[Dict]:
        """
        Verifica condiciones para cerrar posición actual.
        """
        if not self.current_position:
            return None
            
        position_type = self.current_position['type']
        entry_price = self.current_position['entry_price']
        
        # Calcular P&L actual
        if position_type == 'long':
            pnl_pct = (current_price - entry_price) / entry_price
        else:  # short
            pnl_pct = (entry_price - current_price) / entry_price
        
        # Verificar stop loss
        if pnl_pct <= -self.stop_loss_percentage:
            return self._close_position(current_price, 'stop_loss', pnl_pct)
        
        # Verificar take profit
        if pnl_pct >= self.take_profit_percentage:
            return self._close_position(current_price, 'take_profit', pnl_pct)
        
        return None
    
    def _close_position(self, current_price: float, reason: str, pnl_pct: float) -> Dict:
        """
        Cierra la posición actual.
        """
        if not self.current_position:
            return None
            
        position_type = self.current_position['type']
        action = 'sell' if position_type == 'long' else 'buy'
        
        signal = {
            'timestamp': datetime.now(),
            'current_price': current_price,
            'action': action,
            'position_size': self.position_size,
            'pnl_pct': pnl_pct,
            'pnl_usd': self.position_size * pnl_pct,
            'reason': f'Cerrar posición: {reason} (P&L: {pnl_pct:.2%})'
        }
        
        # Limpiar estado de posición
        self.current_position = None
        self.position_entry_price = None
        self.position_size = 0.0
        self.position_type = None
        
        return signal
    
    def get_position_status(self) -> Dict:
        """
        Retorna el estado actual de la posición.
        """
        if not self.current_position:
            return {'status': 'no_position'}
            
        return {
            'status': 'open_position',
            'type': self.current_position['type'],
            'entry_price': self.current_position['entry_price'],
            'size': self.current_position['size'],
            'timestamp': self.current_position['timestamp']
        }
    
    def force_close_position(self, current_price: float) -> Optional[Dict]:
        """
        Fuerza el cierre de la posición actual.
        """
        if not self.current_position:
            return None
            
        position_type = self.current_position['type']
        entry_price = self.current_position['entry_price']
        
        if position_type == 'long':
            pnl_pct = (current_price - entry_price) / entry_price
        else:
            pnl_pct = (entry_price - current_price) / entry_price
            
        return self._close_position(current_price, 'manual_close', pnl_pct)
    
    def update_parameters(self, **kwargs):
        """
        Actualiza los parámetros de la estrategia.
        """
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
                self.logger.info(f"Parámetro {key} actualizado a {value}")
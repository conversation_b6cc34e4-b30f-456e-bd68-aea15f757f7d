"""
Indicadores técnicos avanzados para alimentar modelos de IA
"""
import pandas as pd
import pandas_ta as ta
import numpy as np
from typing import Dict, List, Optional
import logging

logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """Clase para calcular indicadores técnicos avanzados"""
    
    def __init__(self):
        """Inicializar calculadora de indicadores"""
        self._setup_logging()
    
    def _setup_logging(self):
        """Configurar logging"""
        logger.setLevel(logging.INFO)
    
    def calculate_all_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calcular todos los indicadores técnicos
        
        Args:
            df: DataFrame con datos OHLCV
            
        Returns:
            DataFrame con indicadores calculados
        """
        try:
            if df.empty:
                logger.warning("DataFrame vacío para calcular indicadores")
                return df
            
            # Crear copia para no modificar el original
            df_with_indicators = df.copy()
            
            # Indicadores de tendencia
            df_with_indicators = self._add_trend_indicators(df_with_indicators)
            
            # Indicadores de momentum
            df_with_indicators = self._add_momentum_indicators(df_with_indicators)
            
            # Indicadores de volatilidad
            df_with_indicators = self._add_volatility_indicators(df_with_indicators)
            
            # Indicadores de volumen
            df_with_indicators = self._add_volume_indicators(df_with_indicators)
            
            # Indicadores de soporte/resistencia
            df_with_indicators = self._add_support_resistance_indicators(df_with_indicators)
            
            # Limpiar valores infinitos y NaN de forma robusta
            df_with_indicators = self._clean_indicators(df_with_indicators)
            
            logger.info(f"Indicadores calculados: {len(df_with_indicators.columns)} columnas")
            return df_with_indicators
            
        except Exception as e:
            logger.error(f"Error calculando indicadores: {e}")
            return df
    
    def _add_trend_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Agregar indicadores de tendencia"""
        try:
            # Verificar que tenemos suficientes datos
            min_periods = max(26, len(df) // 4)  # Usar al menos 26 o 1/4 de los datos disponibles
            
            # Media móvil simple (SMA) - Con verificación de períodos
            df['sma_20'] = ta.sma(df['close'], length=min(20, len(df)//2)) if len(df) >= 20 else df['close'].rolling(min(10, len(df)//2)).mean()
            df['sma_50'] = ta.sma(df['close'], length=min(50, len(df)//2)) if len(df) >= 50 else df['sma_20']
            
            # Media móvil exponencial (EMA)
            df['ema_12'] = ta.ema(df['close'], length=min(12, len(df)//2)) if len(df) >= 12 else df['close'].ewm(span=min(6, len(df)//2)).mean()
            df['ema_26'] = ta.ema(df['close'], length=min(26, len(df)//2)) if len(df) >= 26 else df['ema_12']
            
            # MACD - Con manejo de errores mejorado
            try:
                if len(df) >= 35:  # Necesario para MACD (26 + 9)
                    macd_result = ta.macd(df['close'], fast=12, slow=26, signal=9)
                    if macd_result is not None and not macd_result.empty:
                        df['macd'] = macd_result.iloc[:, 0] if len(macd_result.columns) > 0 else df['ema_12'] - df['ema_26']
                        df['macd_signal'] = macd_result.iloc[:, 1] if len(macd_result.columns) > 1 else df['macd'].ewm(span=9).mean()
                        df['macd_histogram'] = macd_result.iloc[:, 2] if len(macd_result.columns) > 2 else df['macd'] - df['macd_signal']
                    else:
                        # Calcular manualmente
                        df['macd'] = df['ema_12'] - df['ema_26']
                        df['macd_signal'] = df['macd'].ewm(span=9).mean()
                        df['macd_histogram'] = df['macd'] - df['macd_signal']
                else:
                    # Para pocos datos, usar versión simplificada
                    df['macd'] = df['ema_12'] - df['ema_26']
                    df['macd_signal'] = df['macd'].ewm(span=min(9, len(df)//3)).mean()
                    df['macd_histogram'] = df['macd'] - df['macd_signal']
            except Exception as e:
                logger.warning(f"Error en MACD, calculando manualmente: {e}")
                df['macd'] = df['ema_12'] - df['ema_26']
                df['macd_signal'] = df['macd'].ewm(span=min(9, len(df)//3)).mean()
                df['macd_histogram'] = df['macd'] - df['macd_signal']
            
            # Bandas de Bollinger - Calcular manualmente si falla pandas_ta
            try:
                bollinger = ta.bbands(df['close'])
                if not bollinger.empty and 'BBU_20_2.0' in bollinger.columns:
                    df['bb_upper'] = bollinger['BBU_20_2.0']
                    df['bb_middle'] = bollinger['BBM_20_2.0']
                    df['bb_lower'] = bollinger['BBL_20_2.0']
                    df['bb_width'] = bollinger['BBW_20_2.0']
                    df['bb_percent'] = bollinger['BBP_20_2.0']
                else:
                    # Calcular manualmente
                    df['bb_middle'] = df['close'].rolling(20).mean()
                    bb_std = df['close'].rolling(20).std()
                    df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
                    df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
                    df['bb_width'] = df['bb_upper'] - df['bb_lower']
                    df['bb_percent'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            except Exception as e:
                logger.warning(f"Error en Bollinger Bands, calculando manualmente: {e}")
                # Calcular manualmente
                df['bb_middle'] = df['close'].rolling(20).mean()
                bb_std = df['close'].rolling(20).std()
                df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
                df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
                df['bb_width'] = df['bb_upper'] - df['bb_lower']
                df['bb_percent'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            
            # Ichimoku - Siempre calcular manualmente para mayor estabilidad
            try:
                # Ajustar períodos según datos disponibles
                conv_period = min(9, len(df)//4) if len(df) >= 9 else len(df)//4
                base_period = min(26, len(df)//2) if len(df) >= 26 else len(df)//2
                span_period = min(52, len(df)) if len(df) >= 52 else len(df)
                
                if conv_period > 0 and base_period > 0:
                    df['ichimoku_conversion'] = (df['high'].rolling(conv_period).max() + df['low'].rolling(conv_period).min()) / 2
                    df['ichimoku_base'] = (df['high'].rolling(base_period).max() + df['low'].rolling(base_period).min()) / 2
                    
                    # Verificar que no hay valores None
                    if df['ichimoku_conversion'].isna().all() or df['ichimoku_base'].isna().all():
                        df['ichimoku_conversion'] = df['close']
                        df['ichimoku_base'] = df['close']
                    
                    df['ichimoku_a'] = (df['ichimoku_conversion'].fillna(df['close']) + df['ichimoku_base'].fillna(df['close'])) / 2
                    df['ichimoku_b'] = (df['high'].rolling(span_period).max().fillna(df['high']) + df['low'].rolling(span_period).min().fillna(df['low'])) / 2
                else:
                    # Fallback para muy pocos datos
                    df['ichimoku_conversion'] = df['close']
                    df['ichimoku_base'] = df['close']
                    df['ichimoku_a'] = df['close']
                    df['ichimoku_b'] = df['close']
            except Exception as e:
                logger.warning(f"Error en Ichimoku, usando valores por defecto: {e}")
                df['ichimoku_conversion'] = df['close']
                df['ichimoku_base'] = df['close']
                df['ichimoku_a'] = df['close']
                df['ichimoku_b'] = df['close']
            
            return df
            
        except Exception as e:
            logger.error(f"Error calculando indicadores de tendencia: {e}")
            return df
    
    def _add_momentum_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Agregar indicadores de momentum"""
        try:
            # RSI - Con manejo mejorado
            try:
                rsi_period = min(14, len(df)//2) if len(df) >= 14 else len(df)//2
                if rsi_period > 1:
                    df['rsi'] = ta.rsi(df['close'], length=rsi_period)
                    if df['rsi'].isna().all():
                        df['rsi'] = 50  # Valor neutral por defecto
                else:
                    df['rsi'] = 50
            except:
                df['rsi'] = 50
            
            # Estocástico - Con manejo mejorado
            try:
                if len(df) >= 14:
                    stoch_result = ta.stoch(df['high'], df['low'], df['close'])
                    if stoch_result is not None and not stoch_result.empty:
                        df['stoch_k'] = stoch_result.iloc[:, 0] if len(stoch_result.columns) > 0 else 50
                        df['stoch_d'] = stoch_result.iloc[:, 1] if len(stoch_result.columns) > 1 else 50
                    else:
                        df['stoch_k'] = 50
                        df['stoch_d'] = 50
                else:
                    df['stoch_k'] = 50
                    df['stoch_d'] = 50
            except:
                df['stoch_k'] = 50
                df['stoch_d'] = 50
            
            # Williams %R - Con manejo mejorado
            try:
                williams_period = min(14, len(df)//2) if len(df) >= 14 else len(df)//2
                if williams_period > 1:
                    df['williams_r'] = ta.willr(df['high'], df['low'], df['close'], length=williams_period)
                    if df['williams_r'].isna().all():
                        df['williams_r'] = -50
                else:
                    df['williams_r'] = -50
            except:
                df['williams_r'] = -50
            
            # CCI - Con manejo mejorado
            try:
                cci_period = min(20, len(df)//2) if len(df) >= 20 else len(df)//2
                if cci_period > 1:
                    df['cci'] = ta.cci(df['high'], df['low'], df['close'], length=cci_period)
                    if df['cci'].isna().all():
                        df['cci'] = 0
                else:
                    df['cci'] = 0
            except:
                df['cci'] = 0
            
            # ADX - Con manejo mejorado
            try:
                if len(df) >= 14:
                    adx_result = ta.adx(df['high'], df['low'], df['close'])
                    if adx_result is not None and not adx_result.empty:
                        df['adx'] = adx_result.iloc[:, 0] if len(adx_result.columns) > 0 else 25
                        df['adx_pos'] = adx_result.iloc[:, 1] if len(adx_result.columns) > 1 else 25
                        df['adx_neg'] = adx_result.iloc[:, 2] if len(adx_result.columns) > 2 else 25
                    else:
                        df['adx'] = 25
                        df['adx_pos'] = 25
                        df['adx_neg'] = 25
                else:
                    df['adx'] = 25
                    df['adx_pos'] = 25
                    df['adx_neg'] = 25
            except:
                df['adx'] = 25
                df['adx_pos'] = 25
                df['adx_neg'] = 25
            
            return df
            
        except Exception as e:
            logger.error(f"Error calculando indicadores de momentum: {e}")
            return df
    
    def _add_volatility_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Agregar indicadores de volatilidad"""
        try:
            # ATR (Average True Range) - Con manejo mejorado
            try:
                atr_period = min(14, len(df)//2) if len(df) >= 14 else len(df)//2
                if atr_period > 1:
                    df['atr'] = ta.atr(df['high'], df['low'], df['close'], length=atr_period)
                    if df['atr'].isna().all():
                        df['atr'] = (df['high'] - df['low']).rolling(atr_period).mean()
                else:
                    df['atr'] = df['high'] - df['low']
            except:
                df['atr'] = df['high'] - df['low']
            
            # Keltner Channels - Con manejo mejorado
            try:
                if len(df) >= 20:
                    keltner_result = ta.kc(df['high'], df['low'], df['close'])
                    if keltner_result is not None and not keltner_result.empty:
                        df['kc_upper'] = keltner_result.iloc[:, 0] if len(keltner_result.columns) > 0 else df['close'] * 1.02
                        df['kc_middle'] = keltner_result.iloc[:, 1] if len(keltner_result.columns) > 1 else df['close']
                        df['kc_lower'] = keltner_result.iloc[:, 2] if len(keltner_result.columns) > 2 else df['close'] * 0.98
                    else:
                        # Calcular manualmente
                        df['kc_middle'] = df['close'].rolling(20).mean()
                        atr_20 = df['atr'].rolling(20).mean()
                        df['kc_upper'] = df['kc_middle'] + (atr_20 * 2)
                        df['kc_lower'] = df['kc_middle'] - (atr_20 * 2)
                else:
                    df['kc_middle'] = df['close']
                    df['kc_upper'] = df['close'] * 1.02
                    df['kc_lower'] = df['close'] * 0.98
            except:
                df['kc_middle'] = df['close']
                df['kc_upper'] = df['close'] * 1.02
                df['kc_lower'] = df['close'] * 0.98
            
            # Donchian Channels - Calcular manualmente si falla pandas_ta
            try:
                donchian = ta.donchian(df['high'], df['low'])
                if not donchian.empty and 'DCU_20' in donchian.columns:
                    df['dc_upper'] = donchian['DCU_20']
                    df['dc_middle'] = donchian['DCM_20']
                    df['dc_lower'] = donchian['DCL_20']
                else:
                    # Calcular manualmente
                    df['dc_upper'] = df['high'].rolling(20).max()
                    df['dc_lower'] = df['low'].rolling(20).min()
                    df['dc_middle'] = (df['dc_upper'] + df['dc_lower']) / 2
            except Exception as e:
                logger.warning(f"Error en Donchian Channels, calculando manualmente: {e}")
                # Calcular manualmente
                df['dc_upper'] = df['high'].rolling(20).max()
                df['dc_lower'] = df['low'].rolling(20).min()
                df['dc_middle'] = (df['dc_upper'] + df['dc_lower']) / 2
            
            return df
            
        except Exception as e:
            logger.error(f"Error calculando indicadores de volatilidad: {e}")
            return df
    
    def _add_volume_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Agregar indicadores de volumen"""
        try:
            # Normalizar todos los indicadores para mantener rangos consistentes
            def normalize_series(series):
                if len(series.dropna()) > 1:
                    mean = series.mean()
                    std = series.std()
                    if std > 0:
                        return (series - mean) / std
                return series * 0  # Devuelve serie de ceros si no se puede normalizar
            
            # OBV (On Balance Volume)
            df['obv'] = normalize_series(ta.obv(df['close'], df['volume']))
            
            # VWAP (Volume Weighted Average Price) - Normalizado
            try:
                if len(df) > 20:
                    # Asegurar que el índice sea datetime para VWAP
                    if not isinstance(df.index, pd.DatetimeIndex):
                        df = df.copy()
                        df.index = pd.to_datetime(df.index)
                    vwap_raw = ta.vwap(df['high'], df['low'], df['close'], df['volume'])
                else:
                    vwap_raw = df['close'].rolling(20).mean()
                df['vwap'] = normalize_series(vwap_raw)
            except Exception as e:
                logger.warning(f"Error calculando VWAP: {e}")
                df['vwap'] = 0.0
            
            # Chaikin Money Flow - Ya está normalizado entre -1 y 1
            try:
                df['cmf'] = ta.cmf(df['high'], df['low'], df['close'], df['volume'])
            except Exception as e:
                logger.warning(f"Error calculando CMF: {e}")
                df['cmf'] = 0.0
            
            # Volume Rate of Change - Calcular manualmente con manejo de errores
            try:
                df['vroc'] = df['volume'].pct_change(periods=10).fillna(0) * 100
            except Exception as e:
                logger.warning(f"Error calculando VROC: {e}")
                df['vroc'] = 0.0
            
            # Chaikin Money Flow - Ya está normalizado entre -1 y 1
            df['cmf'] = ta.cmf(df['high'], df['low'], df['close'], df['volume'])
            
            # Volume Rate of Change - ELIMINADO (no usado en features optimizadas)
            # df['vroc'] = df['volume'].pct_change(periods=10) * 100
            
            return df
            
        except Exception as e:
            logger.error(f"Error calculando indicadores de volumen: {e}")
            return df
    
    def _add_support_resistance_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Agregar indicadores de soporte y resistencia"""
        try:
            # Pivot Points - Calcular manualmente
            df['pivot'] = (df['high'] + df['low'] + df['close']) / 3
            df['resistance_1'] = 2 * df['pivot'] - df['low']
            df['resistance_2'] = df['pivot'] + (df['high'] - df['low'])
            df['support_1'] = 2 * df['pivot'] - df['high']
            df['support_2'] = df['pivot'] - (df['high'] - df['low'])
            
            return df
            
        except Exception as e:
            logger.error(f"Error calculando indicadores de soporte/resistencia: {e}")
            return df
    
    def _clean_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Limpiar indicadores de valores infinitos y extremos"""
        try:
            # Solo procesar columnas numéricas
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            
            # Reemplazar infinitos con NaN
            df[numeric_cols] = df[numeric_cols].replace([np.inf, -np.inf], np.nan)
            
            # Reemplazar valores extremadamente grandes (> 1e10) con NaN
            df[numeric_cols] = df[numeric_cols].mask(df[numeric_cols] > 1e10, np.nan)
            df[numeric_cols] = df[numeric_cols].mask(df[numeric_cols] < -1e10, np.nan)
            
            # Estrategia mejorada de relleno de NaN
            for col in numeric_cols:
                if df[col].isna().all():
                    # Si toda la columna es NaN, usar valor por defecto basado en el tipo de indicador
                    if 'rsi' in col or 'stoch' in col:
                        df[col] = 50
                    elif 'williams' in col:
                        df[col] = -50
                    elif 'macd' in col or 'cci' in col:
                        df[col] = 0
                    elif 'adx' in col:
                        df[col] = 25
                    elif any(x in col for x in ['bb_percent', 'fib']):
                        df[col] = 0.5
                    elif any(x in col for x in ['upper', 'resistance']):
                        df[col] = df['high'].fillna(df['close']) * 1.02
                    elif any(x in col for x in ['lower', 'support']):
                        df[col] = df['low'].fillna(df['close']) * 0.98
                    else:
                        df[col] = df['close'].fillna(0)
                else:
                    # Rellenar NaN con estrategia híbrida
                    df[col] = df[col].ffill().bfill()
                    if df[col].isna().any():
                        # Si aún quedan NaN, usar interpolación o media
                        if len(df[col].dropna()) > 1:
                            df[col] = df[col].interpolate(method='linear')
                        df[col] = df[col].fillna(df[col].mean() if not df[col].isna().all() else 0)
            
            # Verificación final de infinitos
            df[numeric_cols] = df[numeric_cols].replace([np.inf, -np.inf], 0)
            
            # Asegurar que todas las columnas tienen datos válidos
            for col in numeric_cols:
                if df[col].isna().any() or np.isinf(df[col]).any():
                    logger.warning(f"Columna {col} tiene valores problemáticos, corrigiendo...")
                    df[col] = df[col].fillna(0).replace([np.inf, -np.inf], 0)
            
            logger.info("Indicadores limpiados de valores infinitos y extremos")
            return df
            
        except Exception as e:
            logger.error(f"Error limpiando indicadores: {e}")
            return df
    
    def get_feature_columns(self) -> List[str]:
        """Obtener lista de columnas de características OPTIMIZADAS - INDICADORES SEGUROS SIN VALORES EXTREMOS"""
        # ✅ OPTIMIZADO: Reducido de 18 a 12 features eliminando indicadores problemáticos
        # ✅ Eliminados indicadores que causan valores extremos y logs de error
        # ✅ Mantenidos solo indicadores estables y confiables
        return [
            # Datos básicos OHLCV (5 features - esenciales)
            'open', 'high', 'low', 'close', 'volume',
            # INDICADORES SEGUROS - SIN VALORES EXTREMOS (8 features seleccionadas)
            'pivot',           # 0.997 - Punto de pivote (mayor correlación)
            'sma_50',          # 0.843 - Media móvil 50 (tendencia estable)
            'rsi',             # 0.565 - Índice de fuerza relativa (0-100 range)
            'stoch_d',         # 0.389 - Estocástico D (0-100 range)
            'macd',            # 0.291 - MACD (valores estables)
            'adx',             # 0.204 - Average Directional Index (0-100 range)
            'macd_histogram',  # 0.153 - MACD Histogram (valores controlados)
            'atr'              # REQUERIDO para target escalado por ATR
        ]
        # ELIMINADOS POR VALORES EXTREMOS:
        # - williams_r: Causa valores extremos (-100 a 0)
        # - adx_neg: Valores negativos problemáticos
        # - cmf: Flujo de dinero con valores extremos
        # - bb_percent: Bollinger Bands % con valores extremos
        # - cci: Commodity Channel Index sin límites definidos
        # 'vroc' - ELIMINADO: Menor correlación (0.037) y causa discrepanciapodri
        # ELIMINADAS por alta correlación/redundancia:
        # - quote_volume, sma_20, ema_12, ema_26, bb_middle, bb_upper, bb_lower
        # - ichimoku_*, stoch_k, adx_pos, kc_*, dc_upper, dc_middle
        # - obv, vwap, resistance_*, support_* (muy correlacionadas con pivot)
    
    def prepare_features_for_ai(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Preparar características para modelos de IA
        
        Args:
            df: DataFrame con indicadores calculados
            
        Returns:
            DataFrame con características preparadas
        """
        try:
            # Obtener columnas de características
            feature_cols = self.get_feature_columns()
            
            # Filtrar solo las columnas disponibles
            available_features = [col for col in feature_cols if col in df.columns]
            
            # Seleccionar características y precio
            ai_features = ['open', 'high', 'low', 'close', 'volume'] + available_features
            
            # Crear DataFrame de características
            features_df = df[ai_features].copy()
            
            # Normalizar características numéricas (excepto precio y volumen)
            price_volume_cols = ['open', 'high', 'low', 'close', 'volume']
            feature_cols_to_normalize = [col for col in available_features if col not in price_volume_cols]

            for col in feature_cols_to_normalize:
                if col in features_df.columns:
                    # Para indicadores con valores extremos (volumen), usar Z-score con clipping
                    if any(x in col for x in ['obv', 'vwap', 'cmf', 'vroc', 'volume']):
                        # Normalización Z-score con clipping para valores extremos
                        mean_val = features_df[col].mean()
                        std_val = features_df[col].std()
                        if std_val > 0:
                            # Aplicar Z-score y luego clipping entre -3 y 3 desviaciones estándar
                            z_scores = (features_df[col] - mean_val) / std_val
                            features_df[col] = np.clip(z_scores, -3, 3)
                        else:
                            features_df[col] = 0
                    else:
                        # Para otros indicadores, usar Min-Max normalizado entre 0-1
                        min_val = features_df[col].min()
                        max_val = features_df[col].max()
                        if max_val > min_val:
                            features_df[col] = (features_df[col] - min_val) / (max_val - min_val)
                        else:
                            features_df[col] = 0
            
            logger.info(f"Características preparadas para IA: {len(features_df.columns)} columnas")
            return features_df
            
        except Exception as e:
            logger.error(f"Error preparando características para IA: {e}")
            return df

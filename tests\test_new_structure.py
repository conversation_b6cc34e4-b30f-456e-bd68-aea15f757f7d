#!/usr/bin/env python3
"""
Script de prueba para verificar la nueva estructura del proyecto
"""
import sys
from pathlib import Path

def test_imports():
    """Probar importaciones de la nueva estructura"""
    try:
        print("Probando importaciones de la nueva estructura...")
        
        # Agregar src al path
        src_path = Path(__file__).parent.parent / "src"
        sys.path.insert(0, str(src_path))
        
        # Probar importaciones
        print("  Importando config...")
        from config.settings import BINANCE_CONFIG, SERVER_CONFIG
        print(f"     Configuracion cargada - Puerto: {SERVER_CONFIG['port']}")
        
        print("  Importando servicios...")
        from services.binance_service import BinanceService
        from services.data_service import DataService
        from services.chart_service import ChartService
        print("     Servicios importados correctamente")
        
        print("  Importando aplicacion...")
        from app import DashboardApp
        print("     Aplicacion importada correctamente")
        
        print("  Importando utilidades...")
        from utils.logging_setup import setup_logging
        print("     Utilidades importadas correctamente")
        
        print("\nTodas las importaciones funcionan correctamente!")
        return True
        
    except ImportError as e:
        print(f"Error de importacion: {e}")
        return False
    except Exception as e:
        print(f"Error inesperado: {e}")
        return False

def test_services():
    """Probar servicios básicos"""
    try:
        print("\nProbando servicios basicos...")
        
        # Agregar src al path
        src_path = Path(__file__).parent.parent / "src"
        sys.path.insert(0, str(src_path))
        
        # Probar servicio de Binance
        print("  Probando servicio de Binance...")
        from services.binance_service import BinanceService
        binance_service = BinanceService()
        
        # Probar conexión
        if binance_service.test_connection():
            print("     Conexion con Binance establecida")
        else:
            print("     Conexion con Binance fallo")
        
        # Probar servicio de datos
        print("  Probando servicio de datos...")
        from services.data_service import DataService
        data_service = DataService()
        print("     Servicio de datos inicializado")
        
        # Probar servicio de gráficas
        print("  Probando servicio de graficas...")
        from services.chart_service import ChartService
        chart_service = ChartService()
        print("     Servicio de graficas inicializado")
        
        print("\nTodos los servicios funcionan correctamente!")
        return True
        
    except Exception as e:
        print(f"Error probando servicios: {e}")
        return False

def test_configuration():
    """Probar configuración"""
    try:
        print("\nProbando configuracion...")
        
        # Agregar src al path
        src_path = Path(__file__).parent.parent / "src"
        sys.path.insert(0, str(src_path))
        
        from config.settings import (
            BINANCE_CONFIG, SERVER_CONFIG, UPDATE_CONFIG, 
            CHART_CONFIG, COLORS, LOGGING_CONFIG
        )
        
        # Verificar configuraciones
        print(f"  Binance: {BINANCE_CONFIG['symbol']} - {BINANCE_CONFIG['interval']}")
        print(f"  Servidor: {SERVER_CONFIG['host']}:{SERVER_CONFIG['port']}")
        print(f"  Actualizacion: {UPDATE_CONFIG['interval_ms']}ms")
        print(f"  Grafica: {CHART_CONFIG['height']}px")
        print(f"  Colores: {len(COLORS)} definidos")
        print(f"  Logging: {LOGGING_CONFIG['level']}")
        
        print("\nConfiguracion cargada correctamente!")
        return True
        
    except Exception as e:
        print(f"Error en configuracion: {e}")
        return False

def main():
    """Función principal de pruebas"""
    print("Iniciando pruebas de la nueva estructura del proyecto...")
    print("=" * 60)
    
    # Ejecutar pruebas
    tests = [
        ("Importaciones", test_imports),
        ("Servicios", test_services),
        ("Configuración", test_configuration)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\nEjecutando: {test_name}")
        print("-" * 40)
        result = test_func()
        results.append((test_name, result))
    
    # Resumen de resultados
    print("\n" + "=" * 60)
    print("RESUMEN DE PRUEBAS")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "PASO" if result else "FALLO"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nResultado: {passed}/{len(results)} pruebas pasaron")
    
    if passed == len(results):
        print("\nTodas las pruebas pasaron! La nueva estructura esta funcionando correctamente.")
        print("   Puedes ejecutar el dashboard con: python main.py")
    else:
        print("\nAlgunas pruebas fallaron. Revisa los errores arriba.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)

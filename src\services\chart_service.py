"""
Servicio para generar gráficas y visualizaciones del dashboard
"""
import logging
from typing import Dict, List, Optional, Tuple
import plotly.graph_objs as go
import pandas as pd
import numpy as np
from dash import html

from config.settings import CHART_CONFIG, COLORS
from config.constants import CHART_TYPES, DATE_FORMATS

logger = logging.getLogger(__name__)

class ChartService:
    """Servicio para generar gráficos del dashboard."""
    
    def _setup_logging(self):
        """Configurar logging para el servicio."""
        pass
    
    def create_trading_signals_chart(self, signals_data: List[Dict]) -> go.Figure:
        """
        Crear gráfica de señales de trading
        
        Args:
            signals_data: Lista de diccionarios con datos de señales
            
        Returns:
            go.Figure: Figura de Plotly con señales de trading
        """
        try:
            if not signals_data:
                return self._create_empty_chart()
            
            fig = go.Figure()
            
            # Separar señales por tipo
            buy_signals = [s for s in signals_data if s.get('signal') in ['COMPRA', 'COMPRA FUERTE', 'COMPRA MODERADA']]
            sell_signals = [s for s in signals_data if s.get('signal') in ['VENTA', 'VENTA FUERTE', 'VENTA MODERADA']]
            hold_signals = [s for s in signals_data if s.get('signal') == 'MANTENER']
            
            # Agregar señales de compra
            if buy_signals:
                fig.add_trace(go.Scatter(
                    x=[s['timestamp'] for s in buy_signals],
                    y=[s['price'] for s in buy_signals],
                    mode='markers',
                    name='🟢 Señales de Compra',
                    marker=dict(
                        size=12,
                        symbol='triangle-up',
                        color='#27AE60',
                        line=dict(width=2, color='white')
                    ),
                    hovertemplate='<b>🟢 SEÑAL DE COMPRA</b><br>' +
                                'Fecha: %{x}<br>' +
                                'Precio: $%{y:,.2f}<br>' +
                                '<extra></extra>'
                ))
            
            # Agregar señales de venta
            if sell_signals:
                fig.add_trace(go.Scatter(
                    x=[s['timestamp'] for s in sell_signals],
                    y=[s['price'] for s in sell_signals],
                    mode='markers',
                    name='🔴 Señales de Venta',
                    marker=dict(
                        size=12,
                        symbol='triangle-down',
                        color='#E74C3C',
                        line=dict(width=2, color='white')
                    ),
                    hovertemplate='<b>🔴 SEÑAL DE VENTA</b><br>' +
                                'Fecha: %{x}<br>' +
                                'Precio: $%{y:,.2f}<br>' +
                                '<extra></extra>'
                ))
            
            # Configurar layout
            fig.update_layout(
                title="<b>Señales de Trading - Últimas 24 Horas</b>",
                xaxis_title="<b>Tiempo</b>",
                yaxis_title="<b>Precio (USDT)</b>",
                template=self.config['template'],
                height=400,
                showlegend=True
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"Error creando gráfica de señales: {e}")
            return self._create_error_chart()
    
    def create_position_status_indicator(self, position_data: Dict) -> html.Div:
        """
        Crear indicador visual del estado de las posiciones
        
        Args:
            position_data: Diccionario con datos de la posición
            
        Returns:
            html.Div: Componente Dash con el estado de la posición
        """
        try:
            if not position_data:
                return html.Div([
                    html.Span("⚪ Sin posición activa", className="badge bg-secondary")
                ])
            
            position_type = position_data.get('type', 'NONE')
            pnl = position_data.get('pnl', 0)
            pnl_pct = position_data.get('pnl_percentage', 0)
            
            # Determinar color y estilo según el tipo de posición y PnL
            if position_type == 'LONG':
                if pnl > 0:
                    badge_class = 'success'
                    icon = '🟢📈'
                elif pnl < 0:
                    badge_class = 'warning'
                    icon = '🟡📈'
                else:
                    badge_class = 'info'
                    icon = '🔵📈'
                text = f"LONG {pnl_pct:+.2f}%"
            elif position_type == 'SHORT':
                if pnl > 0:
                    badge_class = 'success'
                    icon = '🟢📉'
                elif pnl < 0:
                    badge_class = 'warning'
                    icon = '🟡📉'
                else:
                    badge_class = 'info'
                    icon = '🔵📉'
                text = f"SHORT {pnl_pct:+.2f}%"
            else:
                badge_class = 'secondary'
                icon = '⚪'
                text = "Sin posición"
            
            return html.Div([
                html.Span(f"{icon} {text}", className=f"badge bg-{badge_class} me-2"),
                html.Small(f"PnL: ${pnl:+,.2f}", className="text-muted")
            ])
            
        except Exception as e:
            logger.error(f"Error creando indicador de posición: {e}")
            return html.Div([
                html.Span("❌ Error", className="badge bg-danger")
            ])
    
    def create_risk_metrics_chart(self, risk_data: Dict) -> go.Figure:
        """
        Crear gráfica de métricas de riesgo
        
        Args:
            risk_data: Diccionario con métricas de riesgo
            
        Returns:
            go.Figure: Figura de Plotly con métricas de riesgo
        """
        try:
            if not risk_data:
                return self._create_empty_chart()
            
            # Crear gráfica de gauge para el nivel de riesgo
            risk_level = risk_data.get('risk_level', 0)  # 0-100
            
            fig = go.Figure(go.Indicator(
                mode = "gauge+number+delta",
                value = risk_level,
                domain = {'x': [0, 1], 'y': [0, 1]},
                title = {'text': "Nivel de Riesgo"},
                delta = {'reference': 50},
                gauge = {
                    'axis': {'range': [None, 100]},
                    'bar': {'color': "darkblue"},
                    'steps': [
                        {'range': [0, 30], 'color': "lightgreen"},
                        {'range': [30, 70], 'color': "yellow"},
                        {'range': [70, 100], 'color': "red"}
                    ],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': 90
                    }
                }
            ))
            
            fig.update_layout(
                title="<b>Análisis de Riesgo</b>",
                template=self.config['template'],
                height=300
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"Error creando gráfica de riesgo: {e}")
            return self._create_error_chart()

    def __init__(self):
        """Inicializar el servicio de gráficas"""
        self.config = CHART_CONFIG
        self.colors = COLORS
        self._setup_logging()
    
    def _setup_logging(self):
        """Configurar logging para el servicio"""
        logger.setLevel(logging.INFO)
    
    def create_price_chart(self, 
                          df: pd.DataFrame, 
                          chart_type: str = "line",
                          prediction_data: dict = None) -> go.Figure:
        """
        Crear gráfica de precios con predicción
        
        Args:
            df: DataFrame con datos de mercado
            chart_type: Tipo de gráfica (line o candlestick)
            prediction_data: Diccionario con datos de predicción
            
        Returns:
            go.Figure: Figura de Plotly
        """
        try:
            if df.empty:
                return self._create_empty_chart()
            
            # Crear figura base
            fig = go.Figure()
            
            # Agregar gráfica principal según el tipo
            if chart_type == CHART_TYPES["candlestick"]:
                fig = self._add_candlestick_trace(fig, df)
            else:
                fig = self._add_line_trace(fig, df)
            
            # 🧠 AGREGAR PREDICCIÓN SI ESTÁ DISPONIBLE
            if prediction_data and prediction_data.get('price'):
                fig = self._add_prediction_trace(fig, df, prediction_data)
            
            # Configurar layout
            fig = self._configure_chart_layout(fig, chart_type, prediction_data)
            
            logger.info(f"Gráfica de {chart_type} con predicción creada exitosamente")
            return fig
            
        except Exception as e:
            logger.error(f"Error creando gráfica de precios: {e}")
            return self._create_error_chart()
    
    def _add_candlestick_trace(self, fig: go.Figure, df: pd.DataFrame) -> go.Figure:
        """Agregar trazo de velas japonesas"""
        try:
            fig.add_trace(go.Candlestick(
                x=df['timestamp'],
                open=df['open'],
                high=df['high'],
                low=df['low'],
                close=df['close'],
                name='Precio',
                increasing_line_color=self.colors['candlestick_green'],
                decreasing_line_color=self.colors['candlestick_red'],
                increasing_fillcolor=self.colors['candlestick_green'],
                decreasing_fillcolor=self.colors['candlestick_red']
            ))
            return fig
        except Exception as e:
            logger.error(f"Error agregando velas japonesas: {e}")
            return fig
    
    def _add_line_trace(self, fig: go.Figure, df: pd.DataFrame) -> go.Figure:
        """Agregar trazo de línea"""
        try:
            # Línea principal de precios (datos reales históricos y actuales)
            fig.add_trace(go.Scatter(
                x=df['timestamp'],
                y=df['close'],
                mode='lines',
                name='📊 Precio Real BTC/USDT (Histórico)',
                line=dict(
                    color='#2980B9',  # Azul más fuerte
                    width=3
                ),
                hovertemplate='<b>📊 PRECIO REAL BTC/USDT</b><br>' +
                            'Fecha: %{x|%Y-%m-%d %H:%M}<br>' +
                            'Precio: $%{y:,.2f}<br>' +
                            '<i>Datos reales del mercado</i>' +
                            '<extra></extra>'
            ))
            
            # Área sombreada
            fig.add_trace(go.Scatter(
                x=df['timestamp'],
                y=df['close'],
                fill='tonexty',
                fillcolor=f"rgba(46, 134, 193, 0.1)",
                line=dict(width=0),
                showlegend=False
            ))
            return fig
        except Exception as e:
            logger.error(f"Error agregando línea: {e}")
            return fig
    
    def _add_prediction_trace(self, fig: go.Figure, df: pd.DataFrame, prediction_data: dict) -> go.Figure:
        """Agregar trazo de predicción a la gráfica (futura e histórica)"""
        try:
            if not prediction_data or 'price' not in prediction_data:
                return fig
            
            predicted_price = prediction_data['price']
            confidence = prediction_data.get('confidence', 0)
            
            # 🎯 PREDICCIÓN FUTURA MEJORADA
            from datetime import datetime, timedelta
            
            # Manejar caso donde no existe columna timestamp
            if 'timestamp' in df.columns:
                last_timestamp = pd.to_datetime(df['timestamp'].iloc[-1])
            else:
                # Usar índice como timestamp si no existe la columna
                last_timestamp = datetime.now()
            
            last_price = df['close'].iloc[-1]
            
            # Calcular próximo período (15 minutos)
            next_timestamp = last_timestamp + timedelta(minutes=15)
            
            # Información detallada de la predicción
            price_change = predicted_price - last_price
            change_percent = (price_change / last_price) * 100
            direction = "📈 SUBIDA" if price_change > 0 else "📉 BAJADA" if price_change < 0 else "➡️ LATERAL"
            
            # Crear línea de predicción futura más clara y distintiva
            fig.add_trace(go.Scatter(
                x=[last_timestamp, next_timestamp],
                y=[last_price, predicted_price],
                mode='lines+markers',
                name=f'🚀 PREDICCIÓN PRÓXIMOS 15min: {direction} ({change_percent:+.2f}%)',
                line=dict(
                    color='#E74C3C',  # Rojo más fuerte
                    width=5,
                    dash='dash'
                ),
                marker=dict(
                    size=[8, 12],
                    symbol=['circle', 'star'],
                    color=['#E74C3C', '#C0392B'],
                    line=dict(width=2, color='white')
                ),
                hovertemplate='<b>🚀 PREDICCIÓN FUTURA</b><br>' +
                            '<b>Período:</b> Próximos 15 minutos<br>' +
                            '<b>Fecha Objetivo:</b> %{x|%Y-%m-%d %H:%M}<br>' +
                            '<b>Precio Predicho:</b> $%{y:,.2f}<br>' +
                            '<b>Cambio Esperado:</b> ' + f'{change_percent:+.2f}% ({direction})<br>' +
                            '<b>Confianza del Modelo:</b> ' + f'{confidence * 100:.1f}%<br>' +
                            '<b>Diferencia:</b> $' + f'{price_change:+,.2f}<br>' +
                            '<i>Basado en modelo LSTM entrenado</i>' +
                            '<extra></extra>'
            ))
            
            # Agregar anotación con información de la predicción
            fig.add_annotation(
                x=next_timestamp,
                y=predicted_price,
                text=f"<b>PREDICCIÓN</b><br>${predicted_price:,.0f}<br>{direction}<br>{change_percent:+.2f}%",
                showarrow=True,
                arrowhead=2,
                arrowsize=1,
                arrowwidth=2,
                arrowcolor='#FF6B6B',
                ax=50,
                ay=-50,
                bgcolor="rgba(255, 107, 107, 0.9)",
                bordercolor="#FF4444",
                borderwidth=2,
                font=dict(color="white", size=10)
            )
            
            # 🧠 PREDICCIONES HISTÓRICAS REALES (si están disponibles)
            if prediction_data.get('historical_predictions'):
                historical_predictions = prediction_data['historical_predictions']
                
                if historical_predictions:
                    # Extraer datos para la gráfica con manejo seguro
                    timestamps = []
                    predicted_prices = []
                    actual_prices = []
                    
                    for pred in historical_predictions:
                        # Manejar timestamp faltante
                        if 'timestamp' in pred:
                            timestamps.append(pred['timestamp'])
                        else:
                            # Usar índice o timestamp genérico
                            timestamps.append(datetime.now() - timedelta(minutes=15 * len(timestamps)))
                        
                        predicted_prices.append(pred.get('predicted_price', 0))
                        actual_prices.append(pred.get('actual_price', 0))
                    
                    # Agregar línea de predicciones históricas
                    fig.add_trace(go.Scatter(
                        x=timestamps,
                        y=predicted_prices,
                        mode='lines+markers',
                        name=f'🔮 Predicciones Pasadas (Backtesting)',
                        line=dict(
                            color='#FF8C42',  # Naranja más distintivo
                            width=2,
                            dash='dot'
                        ),
                        marker=dict(
                            size=4,
                            symbol='diamond',
                            color='#FF8C42'
                        ),
                        hovertemplate='<b>🔮 PREDICCIÓN HISTÓRICA</b><br>' +
                                    'Fecha: %{x|%Y-%m-%d %H:%M}<br>' +
                                    'Precio Predicho: $%{y:,.2f}<br>' +
                                    'Confianza Modelo: ' + f'{confidence * 100:.1f}%<br>' +
                                    '<i>Predicción hecha en el pasado</i>' +
                                    '<extra></extra>'
                    ))
                    
                    # ELIMINAR LÍNEA DUPLICADA - Los precios reales ya se muestran en la línea principal
                    # Solo agregar puntos de referencia para comparación si hay diferencias significativas
                    errors = [abs(pred - actual) / actual * 100 for pred, actual in zip(predicted_prices, actual_prices)]
                    avg_error = np.mean(errors) if errors else 0
                    
                    # Solo mostrar puntos de referencia si el error promedio es significativo
                    if avg_error > 1.0:  # Solo si hay más de 1% de error promedio
                        fig.add_trace(go.Scatter(
                            x=timestamps,
                            y=actual_prices,
                            mode='markers',
                            name=f'✓ Precios Reales (Error: {avg_error:.1f}%)',
                            marker=dict(
                                size=6,
                                symbol='square',
                                color='#27AE60',
                                line=dict(width=1, color='white')
                            ),
                            hovertemplate='<b>✓ PRECIO REAL</b><br>' +
                                        'Fecha: %{x|%Y-%m-%d %H:%M}<br>' +
                                        'Precio Real: $%{y:,.2f}<br>' +
                                        '<i>Para comparar con predicción</i>' +
                                        '<extra></extra>'
                        ))
                    
                    # Agregar área sombreada para predicciones históricas
                    fig.add_trace(go.Scatter(
                        x=timestamps,
                        y=predicted_prices,
                        fill='tonexty',
                        fillcolor='rgba(255, 179, 102, 0.1)',
                        line=dict(width=0),
                        showlegend=False,
                        hoverinfo='skip'
                    ))
            
            # Área sombreada de confianza para predicción futura
            if confidence > 0.8:  # Solo para predicciones de muy alta confianza
                # Crear área de confianza alrededor de la predicción
                margin = abs(predicted_price - last_price) * 0.1  # 10% de margen
                
                fig.add_trace(go.Scatter(
                    x=[last_timestamp, next_timestamp, next_timestamp, last_timestamp],
                    y=[last_price - margin, predicted_price - margin, predicted_price + margin, last_price + margin],
                    fill='toself',
                    fillcolor='rgba(255, 107, 107, 0.1)',
                    line=dict(width=0),
                    showlegend=False,
                    hoverinfo='skip',
                    name='Área de Confianza'
                ))
            
            logger.info(f"Traza de predicción agregada: ${predicted_price:,.2f} (confianza: {confidence * 100:.1f}%)")
            return fig
            
        except Exception as e:
            logger.error(f"Error agregando predicción: {e}")
            return fig
    
    def _generate_historical_predictions(self, df: pd.DataFrame, confidence: float) -> dict:
        """Generar predicciones históricas para validación del modelo"""
        try:
            if len(df) < 20:  # Necesitamos suficientes datos
                return None
            
            # Usar los últimos 10 períodos para predicciones históricas
            historical_periods = 10
            timestamps = []
            predicted_prices = []
            
            for i in range(historical_periods, 0, -1):
                # Obtener datos hasta el período i
                historical_data = df.iloc[:-i]
                if len(historical_data) < 20:  # Mínimo de datos para predicción
                    continue
                
                # Simular predicción histórica (en un sistema real, usarías el modelo)
                current_price = historical_data['close'].iloc[-1]
                
                # Simular predicción basada en tendencia y volatilidad
                price_change = historical_data['close'].diff().mean()
                volatility = historical_data['close'].std()
                
                # Predicción con ruido simulado
                predicted_change = price_change + (np.random.normal(0, volatility * 0.1))
                predicted_price = current_price + predicted_change
                
                timestamps.append(historical_data['timestamp'].iloc[-1])
                predicted_prices.append(predicted_price)
            
            if timestamps:
                return {
                    'timestamps': timestamps,
                    'predicted_prices': predicted_prices
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error generando predicciones históricas: {e}")
            return None
    
    def _configure_chart_layout(self, fig: go.Figure, chart_type: str, prediction_data: dict = None) -> go.Figure:
        """Configurar layout de la gráfica"""
        try:
            from datetime import datetime
            
            # Crear título dinámico con fecha y hora actual
            now = datetime.now()
            chart_type_name = "Velas Japonesas" if chart_type == CHART_TYPES["candlestick"] else "Líneas"
            
            title_text = f"<b>Gráfica de Precios - 15 Minutos</b><br>"
            title_text += f"<sub>Actualizado: {now.strftime('%Y-%m-%d %H:%M:%S')}</sub>"
            
            # 🧠 AGREGAR INFORMACIÓN DE PREDICCIÓN AL TÍTULO
            if prediction_data and prediction_data.get('price'):
                predicted_price = prediction_data['price']
                confidence = prediction_data.get('confidence', 0)
                historical_count = len(prediction_data.get('historical_predictions', []))
                
                # Calcular dirección de la predicción
                if 'current_price' in prediction_data:
                    current_price = prediction_data['current_price']
                    change = predicted_price - current_price
                    change_percent = (change / current_price) * 100
                    direction_emoji = "📈" if change > 0 else "📉" if change < 0 else "➡️"
                    
                    title_text += f"<br><sub>{direction_emoji} <b>Predicción:</b> ${predicted_price:,.0f} ({change_percent:+.2f}%) | <b>Confianza:</b> {confidence * 100:.1f}%</sub>"
                else:
                    title_text += f"<br><sub>🔮 <b>Predicción:</b> ${predicted_price:,.0f} | <b>Confianza:</b> {confidence * 100:.1f}%</sub>"
            
            fig.update_layout(
                title=dict(
                    text=title_text,
                    x=0.5,  # Centrar título
                    font=dict(size=16)
                ),
                xaxis_title="<b>Fecha/Hora</b>",
                yaxis_title="<b>Precio (USDT)</b>",
                hovermode='x unified',
                template=self.config['template'],
                height=self.config['height'],
                showlegend=True,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="center",
                    x=0.5,
                    bgcolor="rgba(255,255,255,0.8)",
                    bordercolor="rgba(0,0,0,0.2)",
                    borderwidth=1
                ),
                # Mejorar formato de ejes
                xaxis=dict(
                    showgrid=True,
                    gridwidth=1,
                    gridcolor='rgba(128,128,128,0.2)',
                    tickformat='%H:%M<br>%m/%d'
                ),
                yaxis=dict(
                    showgrid=True,
                    gridwidth=1,
                    gridcolor='rgba(128,128,128,0.2)',
                    tickformat='$,.0f'
                ),
                margin=dict(l=50, r=50, t=80, b=50)
            )
            
            # Formatear ejes
            fig.update_xaxes(
                tickformat=DATE_FORMATS['chart'],
                tickangle=0,
                gridcolor='lightgray',
                gridwidth=0.5
            )
            
            fig.update_yaxes(
                gridcolor='lightgray',
                gridwidth=0.5,
                tickformat='.2f'
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"Error configurando layout: {e}")
            return fig
    
    def _create_empty_chart(self) -> go.Figure:
        """Crear gráfica vacía"""
        fig = go.Figure()
        fig.add_annotation(
            text="No hay datos disponibles",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=20, color="gray")
        )
        fig.update_layout(
            title="Sin Datos",
            template=self.config['template'],
            height=self.config['height']
        )
        return fig
    
    def _create_error_chart(self) -> go.Figure:
        """Crear gráfica de error"""
        fig = go.Figure()
        fig.add_annotation(
            text="Error al cargar datos",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=20, color="red")
        )
        fig.update_layout(
            title="Error",
            template=self.config['template'],
            height=self.config['height']
        )
        return fig

    def get_trading_signals(self, predicted_price: float, current_price: float, confidence: float) -> tuple:
        """
        Genera señales de trading basadas en la predicción
        
        Args:
            predicted_price: Precio predicho
            current_price: Precio actual
            confidence: Nivel de confianza
            
        Returns:
            tuple: (trading_signal, trading_recommendation, risk_analysis)
        """
        try:
            price_change_pct = ((predicted_price - current_price) / current_price) * 100
            
            # Determinar señal de trading - AJUSTADO PARA DETECTAR OPORTUNIDADES DEL 2%
            if price_change_pct > 2.0 and confidence > 0.6:  # Reducido umbral de confianza para detectar más oportunidades
                trading_signal = "COMPRA FUERTE"
                trading_recommendation = f"🚀 OPORTUNIDAD ALTA: Precio esperado ${predicted_price:,.2f} (+{price_change_pct:.1f}%) - ¡Aprovechar volatilidad!"
                risk_analysis = "Bajo" if confidence > 0.75 else "Medio"
            elif price_change_pct > 1.5 and confidence > 0.5:  # Detectar oportunidades del 1.5%+
                trading_signal = "COMPRA"
                trading_recommendation = f"📈 BUENA OPORTUNIDAD: Precio esperado ${predicted_price:,.2f} (+{price_change_pct:.1f}%) - Volatilidad favorable"
                risk_analysis = "Medio"
            elif price_change_pct > 0.8 and confidence > 0.4:  # Detectar oportunidades moderadas
                trading_signal = "COMPRA MODERADA"
                trading_recommendation = f"📊 OPORTUNIDAD MODERADA: Precio esperado ${predicted_price:,.2f} (+{price_change_pct:.1f}%) - Considerar entrada"
                risk_analysis = "Medio"
            elif price_change_pct < -2.0 and confidence > 0.6:
                trading_signal = "VENTA FUERTE"
                trading_recommendation = f"🔻 RIESGO ALTO: Precio esperado ${predicted_price:,.2f} ({price_change_pct:.1f}%) - Considerar salida"
                risk_analysis = "Bajo" if confidence > 0.75 else "Medio"
            elif price_change_pct < -1.5 and confidence > 0.5:
                trading_signal = "VENTA"
                trading_recommendation = f"⚠️ PRECAUCIÓN: Precio esperado ${predicted_price:,.2f} ({price_change_pct:.1f}%) - Monitorear posición"
                risk_analysis = "Medio"
            elif price_change_pct < -0.8 and confidence > 0.4:
                trading_signal = "VENTA MODERADA"
                trading_recommendation = f"📉 SEÑAL BAJISTA: Precio esperado ${predicted_price:,.2f} ({price_change_pct:.1f}%) - Evaluar salida"
                risk_analysis = "Medio"
            else:
                trading_signal = "MANTENER"
                trading_recommendation = f"⏸️ MANTENER: Precio esperado ${predicted_price:,.2f} ({price_change_pct:+.1f}%) - Sin oportunidad clara"
                risk_analysis = "Alto" if confidence < 0.4 else "Medio"
                
            return trading_signal, trading_recommendation, risk_analysis
            
        except Exception as e:
            logger.error(f"Error generando señales de trading: {e}")
            return "ERROR", "No se pudo generar recomendación", "Alto"
    
    def create_prediction_indicator(self, predicted_price: float, current_price: float):
        """
        Crea un indicador visual para la predicción
    
        Args:
            predicted_price: Precio predicho
            current_price: Precio actual
            
        Returns:
            Componente Dash (html.Span) con estilos Bootstrap
        """
        try:
            # Manejo de casos sin datos
            if not predicted_price or not current_price:
                return html.Span("⚪ Sin datos", className="badge bg-secondary")
            
            change_pct = ((predicted_price - current_price) / current_price) * 100
            
            if change_pct > 1:
                color_class = 'success'
                icon = '🟢'
                text = f'Alcista (+{change_pct:.1f}%)'
            elif change_pct < -1:
                color_class = 'danger'
                icon = '🔴'
                text = f'Bajista ({change_pct:.1f}%)'
            else:
                color_class = 'warning'
                icon = '🟡'
                text = f'Neutral ({change_pct:+.1f}%)'
            
            return html.Span(f"{icon} {text}", className=f"badge bg-{color_class}")
        except Exception as e:
            logger.error(f"Error creando indicador de predicción: {e}")
            return html.Span("❌ Error", className="badge bg-danger")

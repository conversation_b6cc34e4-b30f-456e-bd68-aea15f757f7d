import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class BacktestingService:
    """Servicio para simular operaciones de trading en tiempo real basadas en predicciones."""
    
    def __init__(self, initial_balance: float = 10000.0):
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.position = 0.0  # Cantidad de BTC en posición
        self.position_entry_price = 0.0
        self.trades_history = []
        self.portfolio_history = []
        self.last_signal = None
        self.last_signal_time = None
        
        # Configuración de trading
        self.min_confidence = 0.6  # Confianza mínima para operar
        self.min_price_change = 0.02  # Cambio mínimo del 2% para operar
        self.max_position_size = 0.8  # Máximo 80% del balance en una posición
        self.stop_loss = 0.05  # Stop loss del 5%
        self.take_profit = 0.10  # Take profit del 10%
        
    def process_signal(self, current_price: float, predicted_price: float, 
                      confidence: float, timestamp: datetime = None) -> Dict:
        """Procesa una señal de trading y ejecuta operaciones simuladas."""
        
        if timestamp is None:
            timestamp = datetime.now()
            
        # Calcular cambio de precio esperado
        price_change_pct = (predicted_price - current_price) / current_price
        
        # Determinar señal
        signal = self._generate_signal(price_change_pct, confidence)
        
        # Ejecutar operación si es necesario
        trade_result = None
        if signal != 'HOLD':
            trade_result = self._execute_trade(signal, current_price, predicted_price, 
                                             confidence, timestamp)
        
        # Actualizar historial del portfolio
        portfolio_value = self._calculate_portfolio_value(current_price)
        self.portfolio_history.append({
            'timestamp': timestamp,
            'balance': self.current_balance,
            'position': self.position,
            'current_price': current_price,
            'portfolio_value': portfolio_value,
            'signal': signal,
            'predicted_price': predicted_price,
            'confidence': confidence
        })
        
        # Mantener solo los últimos 1000 registros
        if len(self.portfolio_history) > 1000:
            self.portfolio_history = self.portfolio_history[-1000:]
            
        self.last_signal = signal
        self.last_signal_time = timestamp
        
        return {
            'signal': signal,
            'trade_executed': trade_result is not None,
            'trade_details': trade_result,
            'portfolio_value': portfolio_value,
            'current_balance': self.current_balance,
            'position': self.position,
            'total_trades': len(self.trades_history)
        }
    
    def _generate_signal(self, price_change_pct: float, confidence: float) -> str:
        """Genera señal de trading basada en predicción y confianza."""
        
        # Verificar confianza mínima
        if confidence < self.min_confidence:
            return 'HOLD'
            
        # Verificar cambio mínimo de precio
        if abs(price_change_pct) < self.min_price_change:
            return 'HOLD'
            
        # Generar señal
        if price_change_pct > 0:
            return 'BUY' if self.position == 0 else 'HOLD'
        else:
            return 'SELL' if self.position > 0 else 'HOLD'
    
    def _execute_trade(self, signal: str, current_price: float, predicted_price: float,
                      confidence: float, timestamp: datetime) -> Dict:
        """Ejecuta una operación de trading simulada."""
        
        trade_result = None
        
        try:
            if signal == 'BUY' and self.position == 0:
                # Calcular cantidad a comprar
                max_investment = self.current_balance * self.max_position_size
                btc_amount = max_investment / current_price
                
                if btc_amount > 0:
                    self.position = btc_amount
                    self.position_entry_price = current_price
                    self.current_balance -= max_investment
                    
                    trade_result = {
                        'type': 'BUY',
                        'amount': btc_amount,
                        'price': current_price,
                        'value': max_investment,
                        'timestamp': timestamp,
                        'predicted_price': predicted_price,
                        'confidence': confidence
                    }
                    
                    self.trades_history.append(trade_result)
                    logger.info(f"Compra simulada: {btc_amount:.6f} BTC a ${current_price:.2f}")
                    
            elif signal == 'SELL' and self.position > 0:
                # Vender toda la posición
                sale_value = self.position * current_price
                profit_loss = sale_value - (self.position * self.position_entry_price)
                profit_loss_pct = (current_price - self.position_entry_price) / self.position_entry_price
                
                trade_result = {
                    'type': 'SELL',
                    'amount': self.position,
                    'price': current_price,
                    'value': sale_value,
                    'entry_price': self.position_entry_price,
                    'profit_loss': profit_loss,
                    'profit_loss_pct': profit_loss_pct,
                    'timestamp': timestamp,
                    'predicted_price': predicted_price,
                    'confidence': confidence
                }
                
                self.current_balance += sale_value
                self.position = 0.0
                self.position_entry_price = 0.0
                
                self.trades_history.append(trade_result)
                logger.info(f"Venta simulada: {trade_result['amount']:.6f} BTC a ${current_price:.2f}, P&L: ${profit_loss:.2f}")
                
        except Exception as e:
            logger.error(f"Error ejecutando trade simulado: {e}")
            
        return trade_result
    
    def _calculate_portfolio_value(self, current_price: float) -> float:
        """Calcula el valor total del portfolio."""
        return self.current_balance + (self.position * current_price)
    
    def get_performance_metrics(self) -> Dict:
        """Calcula métricas avanzadas de rendimiento del backtesting."""
        
        if not self.trades_history:
            return {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0.0,
                'total_profit_loss': 0.0,
                'total_return_pct': 0.0,
                'avg_profit_per_trade': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'volatility': 0.0,
                'profit_factor': 0.0
            }
        
        # Filtrar solo ventas para calcular P&L
        sell_trades = [t for t in self.trades_history if t['type'] == 'SELL']
        
        # Calcular valor actual del portfolio
        current_portfolio_value = self._calculate_portfolio_value(
            self.portfolio_history[-1]['current_price'] if self.portfolio_history else 0
        )
        
        if not sell_trades:
            return {
                'total_trades': len(self.trades_history),
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0.0,
                'total_profit_loss': current_portfolio_value - self.initial_balance,
                'total_return_pct': ((current_portfolio_value - self.initial_balance) / self.initial_balance) * 100,
                'avg_profit_per_trade': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': self._calculate_max_drawdown(),
                'volatility': 0.0,
                'profit_factor': 0.0,
                'current_portfolio_value': current_portfolio_value
            }
        
        # Métricas básicas
        winning_trades = len([t for t in sell_trades if t['profit_loss'] > 0])
        losing_trades = len([t for t in sell_trades if t['profit_loss'] <= 0])
        total_profit_loss = sum(t['profit_loss'] for t in sell_trades)
        
        # Métricas avanzadas
        sharpe_ratio = self._calculate_sharpe_ratio()
        max_drawdown = self._calculate_max_drawdown()
        volatility = self._calculate_volatility()
        profit_factor = self._calculate_profit_factor(sell_trades)
        
        return {
            'total_trades': len(self.trades_history),
            'completed_trades': len(sell_trades),
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': (winning_trades / len(sell_trades)) * 100 if sell_trades else 0,
            'total_profit_loss': total_profit_loss,
            'total_return_pct': ((current_portfolio_value - self.initial_balance) / self.initial_balance) * 100,
            'avg_profit_per_trade': total_profit_loss / len(sell_trades) if sell_trades else 0,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'volatility': volatility,
            'profit_factor': profit_factor,
            'current_portfolio_value': current_portfolio_value
        }
    
    def get_recent_trades(self, limit: int = 10) -> List[Dict]:
        """Obtiene las operaciones más recientes."""
        return self.trades_history[-limit:] if self.trades_history else []
    
    def get_portfolio_history(self, limit: int = 100) -> List[Dict]:
        """Obtiene el historial del portfolio."""
        return self.portfolio_history[-limit:] if self.portfolio_history else []
    
    def _calculate_sharpe_ratio(self) -> float:
        """Calcula el ratio de Sharpe basado en los retornos del portfolio."""
        if len(self.portfolio_history) < 2:
            return 0.0
        
        # Calcular retornos diarios
        returns = []
        for i in range(1, len(self.portfolio_history)):
            prev_value = self.portfolio_history[i-1]['portfolio_value']
            curr_value = self.portfolio_history[i]['portfolio_value']
            if prev_value > 0:
                daily_return = (curr_value - prev_value) / prev_value
                returns.append(daily_return)
        
        if not returns:
            return 0.0
        
        # Calcular Sharpe ratio (asumiendo tasa libre de riesgo = 0)
        mean_return = np.mean(returns)
        std_return = np.std(returns)
        
        if std_return == 0:
            return 0.0
        
        # Anualizar (asumiendo datos diarios)
        sharpe = (mean_return / std_return) * np.sqrt(365)
        return sharpe
    
    def _calculate_max_drawdown(self) -> float:
        """Calcula el drawdown máximo del portfolio."""
        if len(self.portfolio_history) < 2:
            return 0.0
        
        portfolio_values = [p['portfolio_value'] for p in self.portfolio_history]
        peak = portfolio_values[0]
        max_drawdown = 0.0
        
        for value in portfolio_values:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        return max_drawdown * 100  # Retornar como porcentaje
    
    def _calculate_volatility(self) -> float:
        """Calcula la volatilidad de los retornos del portfolio."""
        if len(self.portfolio_history) < 2:
            return 0.0
        
        returns = []
        for i in range(1, len(self.portfolio_history)):
            prev_value = self.portfolio_history[i-1]['portfolio_value']
            curr_value = self.portfolio_history[i]['portfolio_value']
            if prev_value > 0:
                daily_return = (curr_value - prev_value) / prev_value
                returns.append(daily_return)
        
        if not returns:
            return 0.0
        
        # Volatilidad anualizada
        volatility = np.std(returns) * np.sqrt(365) * 100
        return volatility
    
    def _calculate_profit_factor(self, sell_trades: List[Dict]) -> float:
        """Calcula el factor de beneficio (ganancias totales / pérdidas totales)."""
        if not sell_trades:
            return 0.0
        
        total_profits = sum(t['profit_loss'] for t in sell_trades if t['profit_loss'] > 0)
        total_losses = abs(sum(t['profit_loss'] for t in sell_trades if t['profit_loss'] < 0))
        
        if total_losses == 0:
            return float('inf') if total_profits > 0 else 0.0
        
        return total_profits / total_losses
    
    def reset(self):
        """Reinicia el backtesting."""
        self.current_balance = self.initial_balance
        self.position = 0.0
        self.position_entry_price = 0.0
        self.trades_history = []
        self.portfolio_history = []
        self.last_signal = None
        self.last_signal_time = None
        logger.info("Backtesting reiniciado")